<?php

namespace App\Traits;

trait DetectsChanges
{
    public function isDataChanged(array $newData, array $only = [], array $except = []): bool
    {
        $keys = array_keys($newData);

        // Apply 'only' filter
        if (! empty($only)) {
            $keys = array_intersect($keys, $only);
        }

        // Apply 'except' filter
        if (! empty($except)) {
            $keys = array_diff($keys, $except);
        }

        foreach ($keys as $key) {
            if (! array_key_exists($key, $newData)) {
                continue;
            }

            $current = $this->$key instanceof \BackedEnum
                ? $this->$key->value
                : $this->$key;

            $incoming = $newData[$key] instanceof \BackedEnum
                ? $newData[$key]->value
                : $newData[$key];

            if ($current !== $incoming) {
                return true;
            }
        }

        return false;
    }
}
