<?php

namespace App\Contracts\Interfaces;

use App\Models\AcademicYear;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface AcademicYearRepositoryInterface
{
    public function getAllWithRelations(): Collection;

    public function getAll();

    public function getAllPaginated(int $perPage = 10): LengthAwarePaginator;

    public function findById(int $id);

    public function create(array $data): AcademicYear;

    public function update(int $id, array $data);

    public function delete(AcademicYear $academicYear): bool;

    public function getActive(): ?AcademicYear;

    public function getCurrentAcademicYear();

    public function changeStatus(int $id, string $status);

    public function getActiveYears();

    public function hasRelatedData(int $id): bool;

    public function getAcademicYearsList();

    public function getAcademicYearWithRelations(int $id);
}
