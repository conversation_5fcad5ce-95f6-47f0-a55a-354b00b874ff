<?php

namespace App\Traits;

use App\Exceptions\BusinessLogicException;
use App\Helpers\ErrorHandler;
use Illuminate\Support\Facades\DB;
use Throwable;

trait DatabaseTransactionTrait
{
    /**
     * Execute a callback within a database transaction with standardized error handling.
     *
     * @param  callable  $callback  The callback to execute within the transaction
     * @param  string  $errorMessage  The error message to use if an exception occurs
     * @return mixed The result of the callback
     *
     * @throws BusinessLogicException If a business logic exception occurs
     */
    protected function executeInTransaction(callable $callback, string $errorMessage = 'Database error')
    {
        DB::beginTransaction();
        try {
            $result = $callback();
            DB::commit();

            return $result;
        } catch (BusinessLogicException $e) {
            DB::rollBack();
            throw $e;
        } catch (Throwable $e) {
            DB::rollBack();
            ErrorHandler::database($errorMessage, $e);
            // This line will never be executed because ErrorHandler::database throws an exception
        }
    }
}
