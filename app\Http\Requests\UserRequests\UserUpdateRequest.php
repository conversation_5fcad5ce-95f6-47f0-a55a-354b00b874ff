<?php

namespace App\Http\Requests\UserRequests;

use App\Enums\RoleEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UserUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $userId = $this->route('user')->id ?? null;

        if (! $userId) {
            return [
                'username' => ['sometimes', 'string', 'max:100'],
                'name' => ['sometimes', 'string', 'max:255'],
                'email' => ['sometimes', 'string', 'email', 'max:255'],
                'password' => ['nullable', 'string', 'min:8'],
                'phone_number' => ['nullable', 'string', 'max:20'],
                'status' => ['sometimes', 'numeric', 'in:0,1'],
                'role' => ['sometimes', 'string', 'in:'.implode(',', $this->getAllowedRoles())],
            ];
        }

        // Get the user from the route binding
        $user = $this->route('user');
        $currentRole = $user->getRoleNames()->first();
        $isTeacher = $currentRole && str_contains($currentRole, 'teacher');
        $isStudent = $currentRole && str_contains($currentRole, 'student');

        $rules = [
            'username' => ['sometimes', 'string', 'max:100', Rule::unique('users')->ignore($userId)],
            'name' => ['sometimes', 'string', 'max:255'],
            'email' => ['sometimes', 'string', 'email', 'max:255', Rule::unique('users')->ignore($userId)],
            'password' => ['nullable', 'string', 'min:8'],
            'phone_number' => ['nullable', 'string', 'max:20'],
            'status' => ['sometimes', 'numeric', 'in:0,1'],
        ];

        // If user is already a teacher or student, lock the role
        if ($isTeacher || $isStudent) {
            $rules['role'] = ['sometimes', 'string', 'in:'.$currentRole];
        } else {
            // For non-teacher/student users, prevent changing to teacher/student roles
            $rules['role'] = ['sometimes', 'string', 'in:'.implode(',', $this->getAllowedRoles())];
        }

        return $rules;
    }

    /**
     * Get the allowed roles for user update.
     * Excludes teacher and student roles which can only be created through their respective modules.
     *
     * @return array<string>
     */
    protected function getAllowedRoles(): array
    {
        $allRoles = RoleEnum::values();

        return array_filter($allRoles, function ($role) {
            return ! str_contains($role, 'teacher') && ! str_contains($role, 'student');
        });
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'username.unique' => 'Username sudah digunakan.',
            'name.required' => 'Nama lengkap wajib diisi.',
            'email.required' => 'Email wajib diisi.',
            'email.email' => 'Format email tidak valid.',
            'email.unique' => 'Email sudah terdaftar.',
            'password.min' => 'Password minimal 8 karakter.',
            'role.in' => 'Role yang dipilih tidak valid.',
        ];
    }
}
