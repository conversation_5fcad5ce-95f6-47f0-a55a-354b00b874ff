<?php

namespace App\Repositories;

use App\Contracts\Interfaces\LessonHourRepositoryInterface;
use App\Models\LessonHour;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class LessonHourRepository implements LessonHourRepositoryInterface
{
    /**
     * LessonHour model instance
     */
    private LessonHour $lessonHourModel;

    /**
     * LessonHourRepository constructor.
     */
    public function __construct(LessonHour $lessonHourModel)
    {
        $this->lessonHourModel = $lessonHourModel;
    }

    /**
     * Get all lesson hours with optional filtering
     */
    public function getAllLessonHours(array $filters): Collection
    {
        $query = $this->lessonHourModel->with('classroom');
        $query = $this->applyFilters($query, $filters);

        return $query->orderBy('sequence', 'asc')->get();
    }

    /**
     * Get a lesson hour by ID
     *
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
     */
    public function getLessonHourById(int $id): LessonHour
    {
        return $this->lessonHourModel->with('classroom')->findOrFail($id);
    }

    /**
     * Create a new lesson hour
     *
     * @throws \Exception
     */
    public function createLessonHour(array $data): LessonHour
    {
        DB::beginTransaction();
        try {
            $lessonHour = $this->lessonHourModel->create([
                'name' => $data['name'],
                'start_time' => $data['start_time'],
                'end_time' => $data['end_time'],
                'sequence' => $data['sequence'],
                'classroom_id' => $data['classroom_id'] ?? null,
                'shift_id' => $data['shift_id'] ?? null,
            ]);

            DB::commit();

            return $lessonHour;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Create lesson hour failed: '.$e->getMessage());
            throw new \Exception('Gagal menyimpan jam pelajaran: '.$e->getMessage());
        }
    }

    /**
     * Update an existing lesson hour
     *
     * @throws \Exception
     */
    public function updateLessonHour(int $id, array $data): bool
    {
        DB::beginTransaction();
        try {
            $lessonHour = $this->getLessonHourById($id);

            $updateData = [
                'name' => $data['name'] ?? $lessonHour->name,
                'start_time' => $data['start_time'] ?? $lessonHour->start_time,
                'end_time' => $data['end_time'] ?? $lessonHour->end_time,
                'sequence' => $data['sequence'] ?? $lessonHour->sequence,
                'classroom_id' => $data['classroom_id'] ?? $lessonHour->classroom_id,
                'shift_id' => $data['shift_id'] ?? $lessonHour->shift_id,
            ];

            // Check if data has actually changed
            if (
                $lessonHour->name === $updateData['name'] &&
                $lessonHour->start_time === $updateData['start_time'] &&
                $lessonHour->end_time === $updateData['end_time'] &&
                $lessonHour->sequence === $updateData['sequence'] &&
                $lessonHour->classroom_id === $updateData['classroom_id'] &&
                $lessonHour->shift_id === $updateData['shift_id']
            ) {
                DB::rollBack();

                return false;
            }

            $lessonHour->fill($updateData);
            $lessonHour->save();

            DB::commit();

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Update lesson hour failed: '.$e->getMessage());
            throw new \Exception('Gagal memperbarui jam pelajaran: '.$e->getMessage());
        }
    }

    /**
     * Delete a lesson hour
     *
     * @throws \Exception
     */
    public function deleteLessonHour(int $id): bool
    {
        DB::beginTransaction();
        try {
            $lessonHour = $this->getLessonHourById($id);

            // Check if the lesson hour has any class schedules
            if ($lessonHour->classSchedules()->count() > 0) {
                throw new \Exception('Jam pelajaran ini tidak dapat dihapus karena masih digunakan pada jadwal kelas');
            }

            $lessonHour->delete();
            DB::commit();

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Delete lesson hour failed: '.$e->getMessage());
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * Get lesson hours by classroom ID
     * This will return both classroom-specific lesson hours and global lesson hours (where classroom_id is null)
     * If the classroom has a shift, it will only return lesson hours for that shift
     */
    public function getLessonHoursByClassroomId(int $classroomId): Collection
    {
        // Get the classroom with its shift
        $classroom = \App\Models\Classroom::with('shift')->find($classroomId);

        $query = $this->lessonHourModel->where(function ($query) use ($classroomId) {
            $query->where('classroom_id', $classroomId)
                ->orWhereNull('classroom_id'); // Include global lesson hours
        });

        // If classroom has a shift, filter by that shift or null shift
        if ($classroom && $classroom->shift_id) {
            $query->where(function ($query) use ($classroom) {
                $query->where('shift_id', $classroom->shift_id)
                    ->orWhereNull('shift_id'); // Include shift-agnostic lesson hours
            });
        }

        return $query->orderBy('sequence', 'asc')->get();
    }

    /**
     * Apply filters to the query
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    private function applyFilters($query, array $filters)
    {
        // Filter by classroom
        if (! empty($filters['classroom_id'])) {
            $query->where('classroom_id', $filters['classroom_id']);
        }

        // Filter by shift
        if (! empty($filters['shift_id'])) {
            $query->where('shift_id', $filters['shift_id']);
        }

        // Search by name
        if (! empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }

        return $query;
    }

    /**
     * Count total number of lesson hours
     */
    public function count(): int
    {
        return $this->lessonHourModel->count();
    }
}
