<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Buat 1 Admin
        $admin = User::create([
            'username' => 'admin123',
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'photo_path' => null, // Atau berikan URL gambar jika ada
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
            'phone_number' => '081234567888',
            'status' => true,
            'last_login_at' => now(),
        ]);
        $admin->assignRole('admin');

        // Buat 1 Principal
        $principal = User::create([
            'username' => 'principal123',
            'name' => 'Principal User',
            'email' => '<EMAIL>',
            'photo_path' => null, // Atau berikan URL gambar jika ada
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
            'phone_number' => '081234567890',
            'status' => true,
            'last_login_at' => now(),
        ]);
        $principal->assignRole('principal');

        // Buat 1 Treasurer
        $treasurer = User::create([
            'username' => 'treasurer123',
            'name' => 'Treasurer User',
            'email' => '<EMAIL>',
            'photo_path' => null,
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
            'phone_number' => '081234567891',
            'status' => true,
            'last_login_at' => now(),
        ]);
        $treasurer->assignRole('treasurer');

        // // Buat 10 Subject Teachers
        // User::factory(10)->create()->each(function ($user) {
        //     $user->assignRole('subject_teacher');
        // });

        // // Buat 5 Substitute Teachers
        // User::factory(5)->create()->each(function ($user) {
        //     $user->assignRole('substitute_teacher');
        // });

        // // Buat 5 Students
        // User::factory(5)->create()->each(function ($user) {
        //     $user->assignRole('student');
        // });
    }
}
