<?php

namespace App\Services;

use App\Contracts\Interfaces\LeaveRequestRepositoryInterface;
use App\Enums\LeaveStatusEnum;
use App\Exceptions\BusinessLogicException;
use App\Exceptions\DatabaseException;
use App\Exceptions\NotFoundException;
use App\Models\LeaveRequest;
use App\Models\LeaveTask;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;

class LeaveRequestService
{
    /**
     * The leave request repository instance.
     */
    protected LeaveRequestRepositoryInterface $leaveRequestRepository;

    /**
     * Create a new LeaveRequestService instance.
     */
    public function __construct(LeaveRequestRepositoryInterface $leaveRequestRepository)
    {
        $this->leaveRequestRepository = $leaveRequestRepository;
    }

    /**
     * Get all leave requests with optional filtering.
     */
    public function getAll(array $filters = []): Collection
    {
        return $this->leaveRequestRepository->getAll($filters);
    }

    /**
     * Create a new leave request.
     *
     * @throws BusinessLogicException
     * @throws DatabaseException
     */
    public function create(array $data): LeaveRequest
    {
        return DB::transaction(function () use ($data) {
            try {
                // Business logic validation
                $this->validateLeaveRequestData($data);

                // Check for overlapping leaves
                $this->checkForOverlappingLeaves($data);

                // Set default status if not provided
                $data['status'] = $data['status'] ?? LeaveStatusEnum::PENDING->value;

                // Create the leave request
                $leaveRequest = $this->leaveRequestRepository->create($data);

                // Process tasks if provided
                if (! empty($data['tasks']) && is_array($data['tasks'])) {
                    foreach ($data['tasks'] as $taskData) {
                        $taskData['leave_request_id'] = $leaveRequest->id;
                        $this->createLeaveTask($taskData);
                    }
                }

                return $leaveRequest->load('tasks');
            } catch (BusinessLogicException $e) {
                throw $e;
            } catch (Throwable $e) {
                throw new DatabaseException('Gagal membuat pengajuan cuti: '.$e->getMessage(), null, [], [], 0, $e);
            }
        });
    }

    /**
     * Update a leave request.
     *
     * @throws BusinessLogicException
     * @throws DatabaseException
     * @throws NotFoundException
     */
    public function update(int $id, array $data): bool
    {
        return DB::transaction(function () use ($id, $data) {
            try {
                // Check if leave request exists
                $leaveRequest = $this->leaveRequestRepository->findById($id);

                // Business logic validation
                $this->validateLeaveRequestData($data);

                // Check if leave request can be updated
                $this->validateLeaveRequestUpdatePermissions($leaveRequest, $data);

                // Check for overlapping leaves if date is being changed
                if (isset($data['leave_date']) && $data['leave_date'] !== $leaveRequest->leave_date->format('Y-m-d')) {
                    $this->checkForOverlappingLeaves($data, $id);
                }

                // Handle approval fields
                if (isset($data['status']) && in_array($data['status'], [LeaveStatusEnum::APPROVED->value, LeaveStatusEnum::REJECTED->value])) {
                    $data['approved_by'] = $data['approved_by'] ?? auth()->id();
                    $data['approved_at'] = now();
                }

                // Process tasks if provided
                if (! empty($data['tasks']) && is_array($data['tasks'])) {
                    foreach ($data['tasks'] as $taskData) {
                        // If task has an ID, update it; otherwise create a new one
                        if (! empty($taskData['id'])) {
                            $this->updateLeaveTask($taskData['id'], $taskData);
                        } else {
                            $taskData['leave_request_id'] = $leaveRequest->id;
                            $this->createLeaveTask($taskData);
                        }
                    }
                }

                // Delete tasks if needed
                if (! empty($data['deleted_tasks']) && is_array($data['deleted_tasks'])) {
                    foreach ($data['deleted_tasks'] as $taskId) {
                        $this->deleteLeaveTask($taskId, $leaveRequest->id);
                    }
                }

                // Update the leave request
                return $this->leaveRequestRepository->update($id, $data);
            } catch (BusinessLogicException|NotFoundException $e) {
                throw $e;
            } catch (Throwable $e) {
                throw new DatabaseException('Gagal memperbarui pengajuan cuti: '.$e->getMessage(), null, [], [], 0, $e);
            }
        });
    }

    /**
     * Delete a leave request.
     *
     * @throws BusinessLogicException
     * @throws DatabaseException
     * @throws NotFoundException
     */
    public function delete(int $id): bool
    {
        return DB::transaction(function () use ($id) {
            try {
                // Check if leave request exists
                $leaveRequest = $this->leaveRequestRepository->findById($id);

                // Check if leave request can be deleted
                $this->validateLeaveRequestDeletionPermissions($leaveRequest);

                return $this->leaveRequestRepository->delete($id);
            } catch (BusinessLogicException|NotFoundException $e) {
                throw $e;
            } catch (Throwable $e) {
                throw new DatabaseException('Gagal menghapus pengajuan cuti: '.$e->getMessage(), null, [], [], 0, $e);
            }
        });
    }

    /**
     * Find a leave request by ID.
     *
     * @throws NotFoundException
     */
    public function find(int $id): LeaveRequest
    {
        try {
            return $this->leaveRequestRepository->findById($id);
        } catch (Throwable $e) {
            throw new NotFoundException("Pengajuan cuti dengan ID {$id} tidak ditemukan");
        }
    }

    /**
     * Get all leave requests for admin listing (alias for getAll).
     */
    public function getAllLeaveRequests(array $filters = []): Collection
    {
        return $this->getAll($filters);
    }

    /**
     * Create a leave request (alias for create).
     *
     * @throws BusinessLogicException
     * @throws DatabaseException
     */
    public function createLeaveRequest(array $data): LeaveRequest
    {
        return $this->create($data);
    }

    /**
     * Update a leave request (alias for update).
     *
     * @throws BusinessLogicException
     * @throws DatabaseException
     * @throws NotFoundException
     */
    public function updateLeaveRequest(int $id, array $data): bool
    {
        return $this->update($id, $data);
    }

    /**
     * Delete a leave request (alias for delete).
     *
     * @throws BusinessLogicException
     * @throws DatabaseException
     * @throws NotFoundException
     */
    public function deleteLeaveRequest(int $id): bool
    {
        return $this->delete($id);
    }

    /**
     * Get a leave request by ID (alias for find).
     *
     * @throws NotFoundException
     */
    public function getLeaveRequestById(int $id): LeaveRequest
    {
        return $this->find($id);
    }

    /**
     * Validate leave request data for business logic rules.
     *
     * @throws BusinessLogicException
     */
    private function validateLeaveRequestData(array $data): void
    {
        // Validate leave date is not in the past (for new requests)
        if (isset($data['leave_date'])) {
            $leaveDate = new \DateTime($data['leave_date']);
            $today = new \DateTime();
            $today->setTime(0, 0, 0);

            if ($leaveDate < $today) {
                throw new BusinessLogicException('Tanggal cuti tidak boleh di masa lalu');
            }
        }

        // Validate reason length
        if (isset($data['reason']) && strlen($data['reason']) > 500) {
            throw new BusinessLogicException('Alasan cuti tidak boleh lebih dari 500 karakter');
        }

        // Validate leave type
        if (isset($data['leave_type']) && !in_array($data['leave_type'], ['sick', 'official_duty', 'personal', 'other'])) {
            throw new BusinessLogicException('Jenis cuti tidak valid');
        }

        // Validate status
        if (isset($data['status']) && !in_array($data['status'], ['pending', 'approved', 'rejected'])) {
            throw new BusinessLogicException('Status cuti tidak valid');
        }
    }

    /**
     * Check for overlapping leave requests.
     *
     * @throws BusinessLogicException
     */
    private function checkForOverlappingLeaves(array $data, ?int $excludeId = null): void
    {
        if (! isset($data['teacher_id']) || ! isset($data['leave_date'])) {
            return;
        }

        $overlapping = $this->leaveRequestRepository->findOverlappingLeaves(
            $data['teacher_id'],
            $data['leave_date'],
            $excludeId
        );

        if ($overlapping->isNotEmpty()) {
            throw new BusinessLogicException('Terdapat pengajuan cuti yang bertumpang tindih dengan tanggal yang dipilih');
        }
    }

    /**
     * Validate leave request update permissions.
     *
     * @throws BusinessLogicException
     */
    private function validateLeaveRequestUpdatePermissions(LeaveRequest $leaveRequest, array $data): void
    {
        // Cannot update approved or rejected leave requests (except for status changes by admin)
        if (!$leaveRequest->status->allowsModification() && !isset($data['status'])) {
            throw new BusinessLogicException('Pengajuan cuti yang sudah disetujui atau ditolak tidak dapat diubah');
        }

        // Check if leave has already started (cannot modify basic details)
        $today = new \DateTime();
        $leaveDate = new \DateTime($leaveRequest->leave_date->format('Y-m-d'));

        if ($leaveDate <= $today && !isset($data['status'])) {
            throw new BusinessLogicException('Pengajuan cuti yang sudah dimulai tidak dapat diubah');
        }
    }

    /**
     * Validate leave request deletion permissions.
     *
     * @throws BusinessLogicException
     */
    private function validateLeaveRequestDeletionPermissions(LeaveRequest $leaveRequest): void
    {
        // Cannot delete approved leave requests
        if (!$leaveRequest->status->allowsDeletion()) {
            throw new BusinessLogicException('Pengajuan cuti yang sudah disetujui tidak dapat dihapus');
        }

        // Cannot delete if leave has already started
        $today = new \DateTime();
        $leaveDate = new \DateTime($leaveRequest->leave_date->format('Y-m-d'));

        if ($leaveDate <= $today) {
            throw new BusinessLogicException('Pengajuan cuti yang sudah dimulai tidak dapat dihapus');
        }
    }

    /**
     * Create a new leave task.
     *
     * @throws BusinessLogicException
     * @throws DatabaseException
     */
    private function createLeaveTask(array $data): LeaveTask
    {
        try {
            // Validate required fields
            if (empty($data['leave_request_id']) || empty($data['task_description'])) {
                throw new BusinessLogicException('Data tugas tidak lengkap');
            }

            $task = new LeaveTask();
            $task->leave_request_id = $data['leave_request_id'];
            $task->task_description = $data['task_description'];
            $task->attachment_path = $data['attachment_path'] ?? null;
            $task->save();

            return $task;
        } catch (BusinessLogicException $e) {
            throw $e;
        } catch (Throwable $e) {
            throw new DatabaseException('Gagal membuat tugas cuti: '.$e->getMessage());
        }
    }

    /**
     * Update an existing leave task.
     *
     * @throws DatabaseException
     * @throws NotFoundException
     */
    private function updateLeaveTask(int $id, array $data): bool
    {
        try {
            // Find the task
            $task = LeaveTask::find($id);
            if (! $task) {
                throw new NotFoundException("Tugas dengan ID {$id} tidak ditemukan");
            }

            // Update task data
            $task->task_description = $data['task_description'];
            $task->attachment_path = $data['attachment_path'] ?? $task->attachment_path;

            return $task->save();
        } catch (NotFoundException $e) {
            throw $e;
        } catch (Throwable $e) {
            throw new DatabaseException('Gagal memperbarui tugas cuti: '.$e->getMessage());
        }
    }

    /**
     * Delete a leave task.
     *
     * @throws DatabaseException
     * @throws NotFoundException
     */
    private function deleteLeaveTask(int $taskId, int $leaveRequestId): bool
    {
        try {
            // Find the task
            $task = LeaveTask::where('id', $taskId)
                ->where('leave_request_id', $leaveRequestId)
                ->first();

            if (! $task) {
                throw new NotFoundException("Tugas dengan ID {$taskId} tidak ditemukan atau bukan bagian dari pengajuan cuti ini");
            }

            return $task->delete();
        } catch (NotFoundException $e) {
            throw $e;
        } catch (Throwable $e) {
            throw new DatabaseException('Gagal menghapus tugas cuti: '.$e->getMessage());
        }
    }
}