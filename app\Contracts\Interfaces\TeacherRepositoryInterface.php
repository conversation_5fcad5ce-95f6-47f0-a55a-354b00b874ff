<?php

namespace App\Contracts\Interfaces;

use App\Models\Teacher;
use Illuminate\Database\Eloquent\Collection;

interface TeacherRepositoryInterface
{
    /**
     * Count total number of teachers
     */
    public function count(): int;

    /**
     * Get all teachers
     */
    public function getAll(array $filters = []): Collection;

    /**
     * Get all active teachers
     */
    public function getAllActive(): Collection;

    /**
     * Find a teacher by ID
     *
     * @throws \App\Exceptions\NotFoundException
     */
    public function findById(int $id): Teacher;

    /**
     * Create a new teacher
     *
     * @throws \App\Exceptions\BusinessLogicException
     * @throws \App\Exceptions\DatabaseException
     */
    public function create(array $data): Teacher;

    /**
     * Update an existing teacher
     *
     * @throws \App\Exceptions\BusinessLogicException
     * @throws \App\Exceptions\DatabaseException
     * @throws \App\Exceptions\NotFoundException
     */
    public function update(int $id, array $data): bool;

    /**
     * Delete a teacher
     *
     * @throws \App\Exceptions\BusinessLogicException
     * @throws \App\Exceptions\DatabaseException
     * @throws \App\Exceptions\NotFoundException
     */
    public function delete(int $id): bool;

    /**
     * Get available teachers based on filters
     */
    public function getAvailableTeachers(array $filters): Collection;

    /**
     * Get teacher assignments
     */
    public function getTeacherAssignments(int $teacherId): Collection;

    /**
     * Get teacher schedule for a specific date
     */
    public function getTeacherSchedule(int $teacherId, string $date): Collection;

    /**
     * Create a new teacher with user account.
     *
     * @throws \App\Exceptions\BusinessLogicException
     * @throws \App\Exceptions\DatabaseException
     */
    public function createTeacherWithUser(array $data): Teacher;

    /**
     * Update teacher and user data.
     *
     * @throws \App\Exceptions\BusinessLogicException
     * @throws \App\Exceptions\DatabaseException
     * @throws \App\Exceptions\NotFoundException
     */
    public function updateTeacherWithUser(int $id, array $data): bool;
}
