<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        $this->call([
            // 1. Core system data
            RolesAndPermissionsSeeder::class,
            UserSeeder::class,

            // 2. Academic data
            AcademicYearSeeder::class,
            ProgramSeeder::class,
            SubjectSeeder::class,
            ShiftSeeder::class,
            LessonHourSeeder::class,

            // 3. User data
            TeacherSeeder::class,
            StudentSeeder::class,

            // 4. Classroom and related data
            ClassroomSeeder::class,

            // 5. Teacher assignments and schedules
            // TeacherAssignmentSeeder::class,
            // ClassScheduleSeeder::class,

            // 6. Test data (if needed)
            // TestSeeder::class, // Uncomment if you need additional test data
        ]);
    }
}
