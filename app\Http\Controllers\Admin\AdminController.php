<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\AcademicYearService;
use App\Services\AttendanceService;
use App\Services\ClassroomService;
use App\Services\ProgramService;
use App\Services\StudentService;
use App\Services\SubjectService;
use App\Services\TeacherService;
use App\Services\UserService;
use Carbon\Carbon;
use Illuminate\View\View;

final class AdminController extends Controller
{
    public function __construct(
        private readonly StudentService $studentService,
        private readonly TeacherService $teacherService,
        private readonly ClassroomService $classroomService,
        private readonly ProgramService $programService,
        private readonly AcademicYearService $academicYearService,
        private readonly SubjectService $subjectService,
        private readonly AttendanceService $attendanceService,
        private readonly UserService $userService
    ) {
    }

    /**
     * Display admin dashboard
     */
    public function index(): View
    {
        $activeAcademicYear = $this->academicYearService->getActiveAcademicYear();

        $stats = [
            'total_students' => $this->studentService->getTotalStudents(),
            'total_teachers' => $this->teacherService->getTotalTeachers(),
            'active_classrooms' => $this->classroomService->getActiveClassroomsCount(),
            'total_programs' => $this->programService->getTotalPrograms(),
            'total_subjects' => $this->subjectService->getTotalSubjects(),
        ];

        // Get current month for attendance summary
        $currentMonth = Carbon::now()->format('Y-m');

        // Get program stats and subject stats
        $programStats = $this->programService->getProgramsWithClassroomStats();
        $recentLogins = $this->getRecentLogins();

        // Get subject counts by program (placeholder data - in a real app, this would come from the database)
        $subjectsByProgram = [
            ['id' => 1, 'name' => 'Program Reguler', 'count' => 12, 'color' => 'primary'],
            ['id' => 2, 'name' => 'Program Unggulan', 'count' => 15, 'color' => 'success'],
            ['id' => 3, 'name' => 'Program Khusus', 'count' => 8, 'color' => 'info'],
            ['id' => 4, 'name' => 'Program Lainnya', 'count' => 5, 'color' => 'warning'],
        ];

        // Get attendance statistics (placeholder data - in a real app, this would come from the database)
        $attendanceStats = [
            'student_attendance_rate' => 85,
            'teacher_attendance_rate' => 92,
        ];

        return view('admin.dashboard', compact(
            'stats',
            'activeAcademicYear',
            'programStats',
            'recentLogins',
            'subjectsByProgram',
            'attendanceStats'
        ));
    }

    /**
     * Get recent user logins
     */
    private function getRecentLogins(int $limit = 5): array
    {
        $recentLogins = User::whereNotNull('last_login_at')
            ->orderBy('last_login_at', 'desc')
            ->with('roles')
            ->limit($limit)
            ->get()
            ->map(fn($user) => [
                'id' => $user->id,
                'name' => $user->name,
                'avatar' => $user->getAvatarUrl(),
                'role' => $user->roleLabel(),
                'time' => $user->last_login_at->diffForHumans(),
                'time_exact' => $user->last_login_at->format('d M Y H:i'),
            ])
            ->toArray();

        return $recentLogins;
    }
}
