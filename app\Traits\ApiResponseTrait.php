<?php

namespace App\Traits;

use App\Exceptions\BusinessLogicException;
use App\Exceptions\DatabaseException;
use App\Exceptions\NotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

trait ApiResponseTrait
{
    /**
     * Return a success JSON response.
     *
     * @param  array|string  $data
     */
    protected function successResponse(
        $data = [],
        ?string $message = null,
        int $statusCode = Response::HTTP_OK,
        array $headers = []
    ): JsonResponse {
        $response = [
            'success' => true,
        ];

        if ($message !== null) {
            $response['message'] = $message;
        }

        if (! empty($data)) {
            $response['data'] = $data;
        }

        return response()->json($response, $statusCode, $headers);
    }

    /**
     * Return an error JSON response.
     */
    protected function errorResponse(
        string $message,
        int $statusCode = Response::HTTP_BAD_REQUEST,
        ?array $errors = null,
        array $headers = []
    ): JsonResponse {
        $response = [
            'success' => false,
            'message' => $message,
        ];

        if ($errors !== null) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $statusCode, $headers);
    }

    /**
     * Return a validation error JSON response.
     */
    protected function validationErrorResponse(
        array $errors,
        string $message = 'Validasi gagal',
        int $statusCode = Response::HTTP_UNPROCESSABLE_ENTITY,
        array $headers = []
    ): JsonResponse {
        return $this->errorResponse($message, $statusCode, $errors, $headers);
    }

    /**
     * Return a not found JSON response.
     */
    protected function notFoundResponse(
        string $message = 'Data tidak ditemukan',
        array $headers = []
    ): JsonResponse {
        return $this->errorResponse($message, Response::HTTP_NOT_FOUND, null, $headers);
    }

    /**
     * Return an unauthorized JSON response.
     */
    protected function unauthorizedResponse(
        string $message = 'Tidak memiliki akses',
        array $headers = []
    ): JsonResponse {
        return $this->errorResponse($message, Response::HTTP_UNAUTHORIZED, null, $headers);
    }

    /**
     * Return a forbidden JSON response.
     */
    protected function forbiddenResponse(
        string $message = 'Akses ditolak',
        array $headers = []
    ): JsonResponse {
        return $this->errorResponse($message, Response::HTTP_FORBIDDEN, null, $headers);
    }

    /**
     * Return a server error JSON response.
     */
    protected function serverErrorResponse(
        string $message = 'Terjadi kesalahan pada server',
        array $headers = []
    ): JsonResponse {
        return $this->errorResponse($message, Response::HTTP_INTERNAL_SERVER_ERROR, null, $headers);
    }

    /**
     * Execute an API action with standardized error handling.
     *
     * @param  callable  $action  The action to execute
     */
    protected function executeApiAction(callable $action): JsonResponse
    {
        try {
            $result = $action();

            return $this->successResponse(
                $result,
                'Operasi berhasil'
            );
        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                $e->getStatusCode(),
                $e->getErrors(),
                $e->getHeaders()
            );
        } catch (NotFoundException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                $e->getStatusCode(),
                $e->getErrors(),
                $e->getHeaders()
            );
        } catch (DatabaseException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                $e->getStatusCode(),
                $e->getErrors(),
                $e->getHeaders()
            );
        } catch (Throwable $e) {
            // Log the detailed error but return a generic message to the user
            Log::error('API error: '.$e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->serverErrorResponse(
                'Terjadi kesalahan pada sistem. Silakan coba lagi nanti.'
            );
        }
    }
}
