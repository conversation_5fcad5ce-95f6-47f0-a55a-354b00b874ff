<?php

namespace App\Services;

use App\Contracts\Interfaces\UserRepositoryInterface;
use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use App\Exceptions\BusinessLogicException;
use App\Exceptions\DatabaseException;
use App\Exceptions\NotFoundException;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;

class StaffService
{
    public function __construct(
        protected UserRepositoryInterface $userRepository
    ) {
    }

    private function getAllowedRoles(): array
    {
        return array_filter(
            RoleEnum::values(),
            fn($role) => !str_contains($role, 'teacher') && !str_contains($role, 'student')
        );
    }

    public function getAllStaff(array $filters = []): Collection
    {
        $staffRoles = $this->getAllowedRoles();
        $filters['role_in'] = $filters['role_in'] ?? $staffRoles;

        return $this->userRepository->getAllUsers($filters)
            ->filter(fn($user) => in_array($user->getRoleNames()->first(), $staffRoles));
    }

    public function getAllActiveStaff(): Collection
    {
        return $this->getAllStaff(['status' => UserStatus::Active->value]);
    }

    public function getStaffById(int $id): User
    {
        try {
            $user = $this->userRepository->getUserById($id);
            return $user;
        } catch (NotFoundException) {
            throw new NotFoundException("Staf dengan ID {$id} tidak ditemukan");
        } catch (Throwable $e) {
            throw new NotFoundException("Staf dengan ID {$id} tidak ditemukan", null, [], [], 0, $e);
        }
    }

    public function createStaff(array $data): User
    {
        DB::beginTransaction();

        try {
            $this->validateStaffData($data);
            $user = $this->userRepository->createUser($this->prepareUserData($data));
            DB::commit();
            return $user->load('roles');
        } catch (BusinessLogicException | DatabaseException $e) {
            DB::rollBack();
            throw $e;
        } catch (Throwable $e) {
            DB::rollBack();
            throw new DatabaseException('Gagal membuat data staf', null, [], [], 0, $e);
        }
    }

    public function updateStaff(int $id, array $data): bool
    {
        DB::beginTransaction();

        try {
            $user = $this->userRepository->getUserById($id);

            if (isset($data['role'])) {
                $this->validateStaffRole($data['role']);
            }

            if (!$this->userRepository->updateUser($id, $data)) {
                throw new BusinessLogicException('Tidak ada perubahan data yang dilakukan');
            }

            DB::commit();
            return true;
        } catch (BusinessLogicException | DatabaseException | NotFoundException $e) {
            DB::rollBack();
            throw $e;
        } catch (Throwable $e) {
            DB::rollBack();
            throw new DatabaseException('Gagal memperbarui data staf', null, [], [], 0, $e);
        }
    }

    public function deleteStaff(int $id): bool
    {
        DB::beginTransaction();

        try {
            $user = $this->userRepository->getUserById($id);
            $this->validateDeletion($user);

            $result = $this->userRepository->deleteUser($id);
            DB::commit();
            return $result;
        } catch (BusinessLogicException | DatabaseException | NotFoundException $e) {
            DB::rollBack();
            throw $e;
        } catch (Throwable $e) {
            DB::rollBack();
            throw new DatabaseException('Gagal menghapus data staf', null, [], [], 0, $e);
        }
    }

    private function validateStaffData(array $data): void
    {
        if (empty($data['name']) || empty($data['email']) || empty($data['password']) || empty($data['role'])) {
            throw new BusinessLogicException('Semua data wajib harus diisi');
        }

        $this->validateStaffRole($data['role']);
    }

    private function validateStaffRole(string $role): void
    {
        if (!in_array($role, $this->getAllowedRoles())) {
            throw new BusinessLogicException('Role tidak valid untuk staf');
        }
    }

    private function prepareUserData(array $data): array
    {
        return [
            'name' => $data['name'],
            'email' => $data['email'],
            'password' => $data['password'],
            'status' => $data['status'] ?? UserStatus::Active,
            'role' => $data['role'],
            'username' => $data['username'] ?? strtolower(explode('@', $data['email'])[0]),
        ];
    }

    private function validateDeletion(User $user): void
    {
        if ($user->getRoleNames()->isEmpty()) {
            throw new BusinessLogicException('Pengguna tidak memiliki role');
        }

        if ($user->status == UserStatus::Active) {
            throw new BusinessLogicException('Pengguna tidak dapat dihapus karena status aktif');
        }

        if ($user->hasRole(RoleEnum::ADMIN->value)) {
            $adminCount = $this->userRepository->countUsersByRole(RoleEnum::ADMIN->value);
            if ($adminCount <= 1) {
                throw new BusinessLogicException('Tidak dapat menghapus admin terakhir');
            }
        }
    }
}
