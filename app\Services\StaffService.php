<?php

namespace App\Services;

use App\Contracts\Interfaces\UserRepositoryInterface;
use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use App\Exceptions\BusinessLogicException;
use App\Exceptions\DatabaseException;
use App\Exceptions\NotFoundException;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;

class StaffService
{
    public function __construct(
        protected UserRepositoryInterface $userRepository
    ) {
    }

    private function getAllowedRoles(): array
    {
        return array_filter(
            RoleEnum::values(),
            fn($role) => !str_contains($role, 'teacher') && !str_contains($role, 'student')
        );
    }

    /**
     * Get all staff with optional filtering
     */
    public function getAll(array $filters = []): Collection
    {
        $staffRoles = $this->getAllowedRoles();
        $filters['role_in'] ??= $staffRoles;

        return $this->userRepository->getAll($filters)
            ->filter(fn($user) => in_array($user->getRoleNames()->first(), $staffRoles));
    }

    /**
     * Get all staff (legacy method)
     *
     * @deprecated Use getAll instead
     */
    public function getAllStaff(array $filters = []): Collection
    {
        return $this->getAll($filters);
    }

    /**
     * Get all active staff
     */
    public function getAllActiveStaff(): Collection
    {
        return $this->getAll(['status' => UserStatus::Active->value]);
    }

    /**
     * Find a staff member by ID
     */
    public function findById(int $id): User
    {
        $user = $this->userRepository->findById($id);

        // Validate that the user is actually staff
        $staffRoles = $this->getAllowedRoles();
        if (!in_array($user->getRoleNames()->first(), $staffRoles)) {
            throw new NotFoundException("Staf dengan ID {$id} tidak ditemukan");
        }

        return $user;
    }

    /**
     * Get staff by ID (legacy method)
     *
     * @deprecated Use findById instead
     */
    public function getStaffById(int $id): User
    {
        return $this->findById($id);
    }

    /**
     * Create a new staff member
     */
    public function create(array $data): User
    {
        DB::beginTransaction();

        try {
            $this->validateStaffData($data);
            $user = $this->userRepository->create($this->prepareUserData($data));
            DB::commit();
            return $user->load('roles');
        } catch (BusinessLogicException | DatabaseException $e) {
            DB::rollBack();
            throw $e;
        } catch (Throwable $e) {
            DB::rollBack();
            throw new DatabaseException('Gagal membuat data staf', null, [], [], 0, $e);
        }
    }

    /**
     * Create staff (legacy method)
     *
     * @deprecated Use create instead
     */
    public function createStaff(array $data): User
    {
        return $this->create($data);
    }

    /**
     * Update staff information
     */
    public function update(int $id, array $data): bool
    {
        DB::beginTransaction();

        try {
            // Validate that the user exists and is staff
            $this->findById($id);

            if (isset($data['role'])) {
                $this->validateStaffRole($data['role']);
            }

            if (!$this->userRepository->update($id, $data)) {
                throw new BusinessLogicException('Tidak ada perubahan data yang dilakukan');
            }

            DB::commit();
            return true;
        } catch (BusinessLogicException | DatabaseException | NotFoundException $e) {
            DB::rollBack();
            throw $e;
        } catch (Throwable $e) {
            DB::rollBack();
            throw new DatabaseException('Gagal memperbarui data staf', null, [], [], 0, $e);
        }
    }

    /**
     * Update staff (legacy method)
     *
     * @deprecated Use update instead
     */
    public function updateStaff(int $id, array $data): bool
    {
        return $this->update($id, $data);
    }

    /**
     * Delete a staff member
     */
    public function delete(int $id): bool
    {
        DB::beginTransaction();

        try {
            $user = $this->findById($id);
            $this->validateDeletion($user);

            $result = $this->userRepository->delete($id);
            DB::commit();
            return $result;
        } catch (BusinessLogicException | DatabaseException | NotFoundException $e) {
            DB::rollBack();
            throw $e;
        } catch (Throwable $e) {
            DB::rollBack();
            throw new DatabaseException('Gagal menghapus data staf', null, [], [], 0, $e);
        }
    }

    /**
     * Delete staff (legacy method)
     *
     * @deprecated Use delete instead
     */
    public function deleteStaff(int $id): bool
    {
        return $this->delete($id);
    }

    private function validateStaffData(array $data): void
    {
        if (empty($data['name']) || empty($data['email']) || empty($data['password']) || empty($data['role'])) {
            throw new BusinessLogicException('Semua data wajib harus diisi');
        }

        $this->validateStaffRole($data['role']);
    }

    private function validateStaffRole(string $role): void
    {
        if (!in_array($role, $this->getAllowedRoles())) {
            throw new BusinessLogicException('Role tidak valid untuk staf');
        }
    }

    private function prepareUserData(array $data): array
    {
        return [
            'name' => $data['name'],
            'email' => $data['email'],
            'password' => $data['password'],
            'status' => $data['status'] ?? UserStatus::Active,
            'role' => $data['role'],
            'username' => $data['username'] ?? strtolower(explode('@', $data['email'])[0]),
        ];
    }

    private function validateDeletion(User $user): void
    {
        if ($user->getRoleNames()->isEmpty()) {
            throw new BusinessLogicException('Pengguna tidak memiliki role');
        }

        if ($user->status == UserStatus::Active) {
            throw new BusinessLogicException('Pengguna tidak dapat dihapus karena status aktif');
        }

        if ($user->hasRole(RoleEnum::ADMIN->value)) {
            $adminCount = $this->userRepository->countUsersByRole(RoleEnum::ADMIN->value);
            if ($adminCount <= 1) {
                throw new BusinessLogicException('Tidak dapat menghapus admin terakhir');
            }
        }
    }
}
