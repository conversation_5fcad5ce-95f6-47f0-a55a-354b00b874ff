<?php

namespace App\Repositories;

use App\Contracts\Interfaces\UserRepositoryInterface;
use App\Models\User;

class UserRepository implements UserRepositoryInterface
{
    protected User $model;

    public function __construct(User $model)
    {
        $this->model = $model;
    }

    public function getUsersByRole(string $roleName)
    {
        return $this->model->role($roleName)->get();
    }

    public function getUserById(int $userId)
    {
        return $this->model->findOrFail($userId);
    }

    public function deleteUser(int $userId)
    {
        $this->model->destroy($userId);
    }

    public function createUser(array $userDetails)
    {
        return $this->model->create($userDetails);
    }

    public function updateUser(int $userId, array $newDetails)
    {
        return $this->model->whereId($userId)->update($newDetails);
    }

    public function getStudentsForDataTable(array $params)
    {
        $query = $this->getUsersByRole('student');

        if (isset($params['name'])) {
            $query->where('name', 'like', '%' . $params['name'] . '%');
        }

        if (isset($params['email'])) {
            $query->where('email', 'like', '%' . $params['email'] . '%');
        }

        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }

        return $query;
    }

    public function getTeachersForDataTable(array $params)
    {
        $query = $this->getUsersByRole('teacher');

        if (isset($params['name'])) {
            $query->where('name', 'like', '%' . $params['name'] . '%');
        }

        if (isset($params['email'])) {
            $query->where('email', 'like', '%' . $params['email'] . '%');
        }

        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }

        return $query;
    }

    public function getEmployeesForDataTable(array $params)
    {
        // $query = $this->getUsersByRole('employee');
        //employee role is admin, principal, and staff
        $query = $this->model->whereIn('role', ['admin', 'principal', 'staff'])->get();

        if (isset($params['name'])) {
            $query->where('name', 'like', '%' . $params['name'] . '%');
        }

        if (isset($params['email'])) {
            $query->where('email', 'like', '%' . $params['email'] . '%');
        }

        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }

        return $query;
    }
}
