<?php

namespace App\Contracts\Interfaces;

use App\Models\Shift;
use Illuminate\Database\Eloquent\Collection;

interface ShiftRepositoryInterface
{
    /**
     * Get all shifts
     */
    public function getAllShifts(array $filters = []): Collection;

    /**
     * Get a shift by ID
     */
    public function getShiftById(int $id): Shift;

    /**
     * Create a new shift
     */
    public function createShift(array $data): Shift;

    /**
     * Update a shift
     */
    public function updateShift(int $id, array $data): bool;

    /**
     * Delete a shift
     */
    public function deleteShift(int $id): bool;

    /**
     * Get all active shifts
     */
    public function getAllActiveShifts(): Collection;

    /**
     * Count total number of shifts
     */
    public function count(): int;
}
