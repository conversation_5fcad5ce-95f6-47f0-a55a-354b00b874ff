<?php

namespace App\Contracts\Interfaces;

interface LessonHourRepositoryInterface
{
    public function count(): int;

    public function getAllLessonHours(array $filters);

    public function getLessonHourById(int $id);

    public function createLessonHour(array $data);

    public function updateLessonHour(int $id, array $data);

    public function deleteLessonHour(int $id);

    public function getLessonHoursByClassroomId(int $classroomId);
}
