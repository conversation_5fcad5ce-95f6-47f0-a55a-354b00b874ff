<?php

namespace App\Http\Controllers\Admin;

use App\Enums\GenderEnum;
use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use App\Exceptions\BusinessLogicException;
use App\Exceptions\DatabaseException;
use App\Exceptions\NotFoundException;
use App\Exceptions\TeacherException;
use App\Http\Controllers\Controller;
use App\Http\Requests\TeacherRequests\TeacherFilterRequest;
use App\Http\Requests\TeacherRequests\TeacherStoreRequest;
use App\Http\Requests\TeacherRequests\TeacherUpdateRequest;
use App\Services\TeacherService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class TeacherController extends Controller
{
    /**
     * TeacherController constructor
     */
    public function __construct(protected TeacherService $teacherService)
    {
    }

    /**
     * Display a listing of teachers
     */
    public function index(TeacherFilterRequest $request): View|JsonResponse
    {
        try {
            $teachers = $this->teacherService->getAllTeachers($request->validated());

            if ($request->ajax()) {
                return $this->datatableResponse($teachers);
            }

            return view('admin.pages.teacher.index', [
                'statuses' => UserStatus::dropdown(),
                'teachers' => $teachers,
                'roles' => [
                    RoleEnum::SUBJECT_TEACHER->value => RoleEnum::SUBJECT_TEACHER->label(),
                    RoleEnum::SUBSTITUTE_TEACHER->value => RoleEnum::SUBSTITUTE_TEACHER->label(),
                ],
                'genders' => GenderEnum::options(),
            ]);
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'error' => 'Error loading teachers: ' . $e->getMessage()
                ], 500);
            }
            
            abort(500, 'Error loading teachers: ' . $e->getMessage());
        }
    }

    /**
     * Format response for DataTables
     */
    private function datatableResponse($data): JsonResponse
    {
        try {
            return datatables()
                ->of($data)
                ->addIndexColumn()
                ->editColumn('user.name', fn($row) => $row->user->name ?? '-')
                ->editColumn('user.email', fn($row) => $row->user->email ?? '-')
                ->editColumn('user.role', fn($row) => $row->user->roleLabel() ?? '-')
                ->editColumn('gender', function ($row) {
                    try {
                        if (!$row->gender) {
                            return '-';
                        }
                        
                        // Temporarily simplify to debug
                        if ($row->gender instanceof GenderEnum) {
                            return $row->gender->value === 'male' ? 'Laki-laki' : 'Perempuan';
                        }
                        
                        // Fallback for string values
                        return $row->gender === 'male' ? 'Laki-laki' : 'Perempuan';
                    } catch (\Exception $e) {
                        \Log::error('Gender column error: ' . $e->getMessage());
                        return '-';
                    }
                })
                ->editColumn('birth_date', fn($row) => $row->birth_date ? date('d/m/Y', strtotime($row->birth_date)) : '-')
                ->editColumn('status', function ($row) {
                    try {
                        return '<span class="badge bg-' . $row->user->status->color() . ' text-uppercase">' . $row->user->status->label() . '</span>';
                    } catch (\Exception $e) {
                        return '<span class="badge bg-secondary">Unknown</span>';
                    }
                })
                ->addColumn(
                    'action',
                    fn($row) => view('admin.components.button-actions-v2', [
                        'id' => $row->id,
                        'edit' => route('admin.teachers.edit', $row->id),
                        'destroy' => route('admin.teachers.destroy', $row->id),
                    ])
                )
                ->rawColumns(['status', 'action'])
                ->make(true);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Error processing data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show the form for creating a new teacher
     */
    public function create(): View
    {
        return view('admin.pages.teacher.create', [
            'roles' => [
                RoleEnum::SUBJECT_TEACHER->value => RoleEnum::SUBJECT_TEACHER->label(),
                RoleEnum::SUBSTITUTE_TEACHER->value => RoleEnum::SUBSTITUTE_TEACHER->label(),
            ],
            'statuses' => UserStatus::dropdown(),
        ]);
    }

    /**
     * Store a newly created teacher
     */
    public function store(TeacherStoreRequest $request): JsonResponse
    {
        try {
            $teacher = $this->teacherService->createTeacher($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Data guru berhasil dibuat',
                'data' => $teacher,
            ]);
        } catch (TeacherException | BusinessLogicException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_BAD_REQUEST);
        } catch (DatabaseException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            return $this->errorResponse('Terjadi kesalahan saat membuat data guru', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Show the form for editing the specified teacher
     */
    public function edit(int $id): View
    {
        try {
            $teacher = $this->teacherService->getTeacherById($id);

            return view('admin.pages.teacher.edit', [
                'teacher' => $teacher,
                'roles' => [
                    RoleEnum::SUBJECT_TEACHER->value => RoleEnum::SUBJECT_TEACHER->label(),
                    RoleEnum::SUBSTITUTE_TEACHER->value => RoleEnum::SUBSTITUTE_TEACHER->label(),
                ],
                'statuses' => UserStatus::dropdown(),
                'currentRole' => $teacher->user->getRoleNames()->first(),
            ]);
        } catch (NotFoundException $e) {
            abort(Response::HTTP_NOT_FOUND, $e->getMessage());
        }
    }

    /**
     * Update the specified teacher
     */
    public function update(TeacherUpdateRequest $request, int $id): JsonResponse
    {
        try {
            $this->teacherService->updateTeacher($id, $request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Data guru berhasil diperbarui',
            ]);
        } catch (TeacherException | BusinessLogicException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        } catch (NotFoundException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (DatabaseException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            return $this->errorResponse('Terjadi kesalahan saat memperbarui data guru', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified teacher
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->teacherService->deleteTeacher($id);

            return response()->json([
                'success' => true,
                'message' => 'Data guru berhasil dihapus',
            ]);
        } catch (TeacherException | BusinessLogicException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_BAD_REQUEST);
        } catch (NotFoundException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (DatabaseException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            return $this->errorResponse('Terjadi kesalahan saat menghapus data guru', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Display the specified teacher
     */
    public function show(int $id): View
    {
        try {
            $teacher = $this->teacherService->getTeacherById($id);

            return view('admin.pages.teacher.show', [
                'teacher' => $teacher,
                'roles' => [
                    RoleEnum::SUBJECT_TEACHER->value => RoleEnum::SUBJECT_TEACHER->label(),
                    RoleEnum::SUBSTITUTE_TEACHER->value => RoleEnum::SUBSTITUTE_TEACHER->label(),
                ],
                'currentRole' => $teacher->user->getRoleNames()->first(),
            ]);
        } catch (NotFoundException $e) {
            abort(Response::HTTP_NOT_FOUND, $e->getMessage());
        }
    }

    /**
     * Change the status of a teacher's account
     */
    public function changeStatus(Request $request, int $id): JsonResponse
    {
        $request->validate(['status' => 'required|boolean']);

        try {
            $teacher = $this->teacherService->changeTeacherStatus($id, $request->status);
            $statusLabel = $teacher->user->status->label();

            return response()->json([
                'success' => true,
                'message' => "Status guru berhasil diubah menjadi {$statusLabel}",
            ]);
        } catch (TeacherException | BusinessLogicException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_BAD_REQUEST);
        } catch (NotFoundException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (\Exception $e) {
            return $this->errorResponse('Terjadi kesalahan saat mengubah status guru', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Generate standardized error response
     */
    protected function errorResponse(string $message, int $statusCode = Response::HTTP_BAD_REQUEST, ?array $errors = null, array $headers = []): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'errors' => $errors,
        ], $statusCode, $headers);
    }
}
