<?php

namespace App\Services;

use App\Contracts\Interfaces\UserRepositoryInterface;
use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use App\Exceptions\BusinessLogicException;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class UserService
{
    /**
     * The user repository instance.
     */
    protected UserRepositoryInterface $userRepository;

    /**
     * Create a new UserService instance.
     */
    public function __construct(UserRepositoryInterface $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    /**
     * Get all users with filtering.
     */
    public function getAllUsers(array $filters = []): Collection
    {
        return $this->userRepository->getAllUsers($filters);
    }

    /**
     * Get all active users.
     */
    public function getAllActiveUsers(): Collection
    {
        return $this->userRepository->getAllUsers(['status' => UserStatus::Active->value]);
    }

    /**
     * Get a user by ID.
     */
    public function getUserById(int $id): User
    {
        return $this->userRepository->getUserById($id);
    }

    /**
     * Create a new user with validation and business rules.
     */
    public function createUser(array $data): User
    {
        // Business validation rules
        if (isset($data['role']) && $data['role'] === RoleEnum::ADMIN->value) {
            $adminCount = $this->userRepository->countUsersByRole(RoleEnum::ADMIN->value);

            if ($adminCount >= 5) {
                throw new BusinessLogicException('Maksimal jumlah pengguna admin (5) telah tercapai.');
            }
        }

        return DB::transaction(function () use ($data) {
            return $this->userRepository->createUser($data);
        });
    }

    /**
     * Update user information.
     */
    public function updateUser(int $id, array $data): bool
    {
        // Business validation - prevent updating to admin role if limit reached
        if (isset($data['role']) && $data['role'] === RoleEnum::ADMIN->value) {
            $user = $this->getUserById($id);

            if ($user->getRoleNames()->first() !== RoleEnum::ADMIN->value) {
                $adminCount = $this->userRepository->countUsersByRole(RoleEnum::ADMIN->value);

                if ($adminCount >= 5) {
                    throw new BusinessLogicException('Maksimal jumlah pengguna admin (5) telah tercapai.');
                }
            }
        }

        return DB::transaction(function () use ($id, $data) {
            return $this->userRepository->updateUser($id, $data);
        });
    }

    /**
     * Delete a user.
     */
    public function deleteUser(int $id): bool
    {
        return DB::transaction(function () use ($id) {
            $user = $this->userRepository->getUserById($id);

            // Business validation - cannot delete active user
            if ($user->status === UserStatus::Active) {
                throw new BusinessLogicException('Pengguna aktif tidak dapat dihapus! Nonaktifkan terlebih dahulu.');
            }

            // Business validation - cannot delete yourself
            if ($user->id === Auth::id()) {
                throw new BusinessLogicException('Tidak dapat menghapus akun sendiri.');
            }

            return $this->userRepository->deleteUser($id);
        });
    }

    /**
     * Change user status.
     */
    public function changeStatus(int $id, bool $status): bool
    {
        return DB::transaction(function () use ($id, $status) {
            $user = $this->userRepository->getUserById($id);

            // Business validation - prevent changing own status
            if ($user->id === Auth::id()) {
                throw new BusinessLogicException('Tidak dapat mengubah status akun sendiri.');
            }

            // Business rule: don't allow deactivating the last active admin
            if (!$status) {
                if ($user->getRoleNames()->first() === RoleEnum::ADMIN->value) {
                    $activeAdminCount = $this->userRepository->countActiveUsersByRole(RoleEnum::ADMIN->value);

                    if ($activeAdminCount <= 1) {
                        throw new BusinessLogicException('Tidak dapat menonaktifkan akun admin terakhir.');
                    }
                }
            }

            return $this->userRepository->changeStatus($id, $status);
        });
    }

    /**
     * Verify user credentials and update last login timestamp.
     */
    public function verifyCredentials(string $username, string $password): ?User
    {
        $user = $this->userRepository->verifyCredentials($username, $password);

        if ($user) {
            // Update last login timestamp
            DB::transaction(function () use ($user) {
                $user->last_login_at = now();
                $user->save();
            });
        }

        return $user;
    }
}
