<?php

namespace App\Http\Requests\StaffRequests;

use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use Illuminate\Validation\Rules\Password;
use Illuminate\Foundation\Http\FormRequest;

class StaffStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'username' => ['required', 'string', 'max:255', 'unique:users,username', 'alpha_dash'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email'],
            'password' => ['required', 'string', Password::defaults()],
            'role' => ['required', 'string', 'in:' . implode(',', $this->getAllowedRoles())],
            'status' => ['required', 'string', 'in:' . implode(',', $this->getAllowedStatuses())],
        ];
    }

    public function getAllowedRoles(): array
    {
        $allRoles = RoleEnum::values();

        return array_filter($allRoles, fn($role) => !str_contains($role, 'teacher') && !str_contains($role, 'student'));
    }

    public function getAllowedStatuses(): array
    {
        return UserStatus::values();
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes()
    {
        return [
            'name' => 'Nama',
            'username' => 'Username',
            'email' => 'Email',
            'password' => 'Password',
            'role' => 'Role',
            'status' => 'Status',
        ];
    }

    /**
     * Get custom error messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages()
    {
        return [
            'role.in' => 'Role yang dipilih tidak valid untuk staf.',
            'status.in' => 'Status yang dipilih tidak valid.',
            'username.unique' => 'Username sudah digunakan.',
            'username.alpha_dash' => 'Username hanya boleh berisi huruf, angka, dash, dan underscore.',
        ];
    }
}
