<?php

namespace App\Services;

use App\Contracts\Interfaces\ClassroomRepositoryInterface;
use App\Contracts\Interfaces\SubjectRepositoryInterface;
use App\Contracts\Interfaces\TeacherAssignmentRepositoryInterface;
use App\Contracts\Interfaces\TeacherRepositoryInterface;
use App\Exceptions\TeacherAssignmentException;
use App\Models\TeacherAssignment;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class TeacherAssignmentService
{
    /**
     * Create a new TeacherAssignmentService instance.
     */
    public function __construct(
        protected TeacherAssignmentRepositoryInterface $teacherAssignmentRepository,
        protected TeacherRepositoryInterface $teacherRepository,
        protected SubjectRepositoryInterface $subjectRepository,
        protected ClassroomRepositoryInterface $classroomRepository
    ) {
    }

    /**
     * Get all teacher assignments with optional filtering.
     */
    public function getAllTeacherAssignments(array $filters = []): Collection
    {
        return $this->teacherAssignmentRepository->getAllTeacherAssignments($filters);
    }

    /**
     * Get a teacher assignment by ID.
     */
    public function getTeacherAssignmentById(int $id): TeacherAssignment
    {
        return $this->teacherAssignmentRepository->getTeacherAssignmentById($id);
    }

    /**
     * Create a new teacher assignment.
     */
    public function createTeacherAssignment(array $data): TeacherAssignment
    {
        return DB::transaction(function () use ($data) {
            // Business validation
            $this->validateTeacherAssignmentData($data);

            // Check for conflicts
            $this->validateTeacherAssignmentConflicts($data);

            return $this->teacherAssignmentRepository->createTeacherAssignment($data);
        });
    }

    /**
     * Update an existing teacher assignment.
     */
    public function updateTeacherAssignment(int $id, array $data): bool
    {
        return DB::transaction(function () use ($id, $data) {
            // Business validation
            $this->validateTeacherAssignmentData($data, $id);

            // Check for conflicts (excluding current assignment)
            $this->validateTeacherAssignmentConflicts($data, $id);

            return $this->teacherAssignmentRepository->updateTeacherAssignment($id, $data);
        });
    }

    /**
     * Delete a teacher assignment.
     */
    public function deleteTeacherAssignment(int $id): bool
    {
        return DB::transaction(function () use ($id) {
            // Business validation for deletion
            $this->validateTeacherAssignmentDeletion($id);

            return $this->teacherAssignmentRepository->deleteTeacherAssignment($id);
        });
    }

    /**
     * Get teacher assignments for a specific teacher.
     */
    public function getTeacherAssignmentsByTeacher(int $teacherId, array $filters = []): Collection
    {
        // Validate teacher exists
        $this->teacherRepository->findById($teacherId);

        return $this->teacherAssignmentRepository->getTeacherAssignmentsByTeacherId($teacherId, $filters);
    }

    /**
     * Validate teacher assignment data for business rules
     */
    private function validateTeacherAssignmentData(array $data, ?int $excludeId = null): void
    {
        // Validate teacher exists and is active
        if (!empty($data['teacher_id'])) {
            try {
                $teacher = $this->teacherRepository->findById($data['teacher_id']);
                if (!$teacher) {
                    throw TeacherAssignmentException::validationError('Guru tidak ditemukan');
                }
            } catch (\Exception $e) {
                throw TeacherAssignmentException::validationError('Guru tidak valid: ' . $e->getMessage());
            }
        }

        // Validate subject exists if provided
        if (!empty($data['subject_id'])) {
            try {
                $subject = $this->subjectRepository->getSubjectById($data['subject_id']);
                if (!$subject) {
                    throw TeacherAssignmentException::validationError('Mata pelajaran tidak ditemukan');
                }
            } catch (\Exception $e) {
                throw TeacherAssignmentException::validationError('Mata pelajaran tidak valid: ' . $e->getMessage());
            }
        }

        // Validate classroom exists
        if (!empty($data['classroom_id'])) {
            try {
                $classroom = $this->classroomRepository->findById($data['classroom_id']);
                if (!$classroom) {
                    throw TeacherAssignmentException::validationError('Kelas tidak ditemukan');
                }
            } catch (\Exception $e) {
                throw TeacherAssignmentException::validationError('Kelas tidak valid: ' . $e->getMessage());
            }
        }
    }

    /**
     * Validate teacher assignment conflicts
     */
    private function validateTeacherAssignmentConflicts(array $data, ?int $excludeId = null): void
    {
        // Check for duplicate assignment
        if ($this->teacherAssignmentRepository->hasDuplicateAssignment($data, $excludeId)) {
            throw TeacherAssignmentException::duplicateAssignment('Guru sudah ditugaskan untuk mata pelajaran dan kelas yang sama');
        }

        // Check homeroom teacher conflict
        if (!empty($data['is_homeroom_teacher']) && $data['is_homeroom_teacher']) {
            if ($this->teacherAssignmentRepository->hasHomeroomTeacher($data['classroom_id'], $data['academic_year_id'], $excludeId)) {
                throw TeacherAssignmentException::homeroomTeacherConflict('Kelas ini sudah memiliki wali kelas');
            }
        }
    }

    /**
     * Validate that a teacher assignment can be deleted
     */
    private function validateTeacherAssignmentDeletion(int $id): void
    {
        // Check if there are active class schedules using this assignment
        if ($this->teacherAssignmentRepository->hasActiveClassSchedules($id)) {
            throw TeacherAssignmentException::cannotDelete('Tidak dapat menghapus penugasan guru karena masih digunakan dalam jadwal kelas');
        }
    }
}
