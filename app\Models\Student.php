<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Student extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'nis',
        'nisn',
        'birth_place',
        'birth_date',
        'gender',
        'religion',
        'address',
        'phone',
        'parent_name',
        'parent_phone',
        'parent_occupation',
        'parent_address',
        'entry_year',
        'profile_picture',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'birth_date' => 'date',
        'entry_year' => 'integer',
    ];

    /**
     * Get the user that owns the student.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * The classrooms that belong to the student.
     */
    public function classrooms(): BelongsToMany
    {
        return $this->belongsToMany(Classroom::class, 'classroom_students')
            ->withPivot('academic_year_id')
            ->using(ClassroomStudent::class)
            ->withTimestamps();
    }

    /**
     * Get the student's current classroom (in the active academic year).
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function getCurrentClassroomAttribute()
    {
        $activeAcademicYear = AcademicYear::where('status', 'active')->first();

        if (! $activeAcademicYear) {
            return null;
        }

        return $this->classrooms()
            ->wherePivot('academic_year_id', $activeAcademicYear->id)
            ->with('academicYear')
            ->first();
    }
}
