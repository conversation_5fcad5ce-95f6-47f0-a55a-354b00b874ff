<?php

namespace App\Repositories;

use App\Contracts\Interfaces\ClassroomRepositoryInterface;
use App\Models\Classroom;
use App\Models\Student;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ClassroomRepository implements ClassroomRepositoryInterface
{
    protected $classroom;

    public function __construct(Classroom $classroom)
    {
        $this->classroom = $classroom;
    }

    /**
     * Get all classrooms with optional filtering.
     *
     * @param  array  $filters  Optional filters
     * @return Collection
     */
    public function getAll(array $filters = [])
    {
        $query = $this->classroom->with(['program', 'teacher.user', 'academicYear', 'shift']);

        // Apply program filter
        if (! empty($filters['program_id'])) {
            $query->where('program_id', $filters['program_id']);
        }

        // Apply level filter
        if (! empty($filters['level'])) {
            $query->where('level', $filters['level']);
        }

        // Apply status filter
        if (! empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // Apply search filter
        if (! empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhereHas('program', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%");
                    })
                    ->orWhereHas('teacher.user', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%");
                    });
            });
        }

        return $query->get();
    }

    /**
     * Get active classrooms.
     *
     * @return Collection
     */
    public function getActiveClassrooms()
    {
        return $this->classroom->where('status', 'active')
            ->with(['program', 'teacher.user', 'academicYear', 'shift'])
            ->get();
    }

    /**
     * Get classrooms by academic year.
     *
     * @return Collection
     */
    public function getByAcademicYear(int $academicYearId)
    {
        return $this->classroom->where('academic_year_id', $academicYearId)
            ->with(['program', 'teacher.user', 'shift'])
            ->get();
    }

    /**
     * Get active classrooms by academic year.
     *
     * @return Collection
     */
    public function getActiveByAcademicYear(int $academicYearId)
    {
        return $this->classroom->where('academic_year_id', $academicYearId)
            ->where('status', 'active')
            ->with(['program', 'teacher.user', 'shift'])
            ->get();
    }

    /**
     * Find a classroom by ID.
     *
     * @return Classroom
     */
    public function findById(int $id)
    {
        return $this->classroom->with(['program', 'teacher.user', 'academicYear', 'shift'])->findOrFail($id);
    }

    /**
     * Create a new classroom.
     *
     * @return Classroom
     */
    public function create(array $data)
    {
        DB::beginTransaction();
        try {
            $classroom = $this->classroom->create($data);
            DB::commit();

            return $classroom;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Create classroom failed: '.$e->getMessage());
            throw new \Exception('Gagal membuat kelas: '.$e->getMessage());
        }
    }

    /**
     * Update a classroom.
     *
     * @return bool
     */
    public function update(int $id, array $data)
    {
        DB::beginTransaction();
        try {
            $classroom = $this->findById($id);
            $result = $classroom->update($data);
            DB::commit();

            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Update classroom failed: '.$e->getMessage());
            throw new \Exception('Gagal memperbarui kelas: '.$e->getMessage());
        }
    }

    /**
     * Change the teacher assigned to a classroom.
     *
     * @return bool
     */
    public function changeTeacher(int $classroomId, int $teacherId)
    {
        DB::beginTransaction();
        try {
            $classroom = $this->findById($classroomId);
            $classroom->teacher_id = $teacherId;
            $result = $classroom->save();
            DB::commit();

            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Change classroom teacher failed: '.$e->getMessage());
            throw new \Exception('Gagal mengubah guru kelas: '.$e->getMessage());
        }
    }

    /**
     * Get students in a classroom.
     *
     * @return Collection
     */
    public function getClassroomStudents(int $classroomId)
    {
        $classroom = $this->findById($classroomId);

        return $classroom->currentStudents()->with('user')->get();
    }

    /**
     * Get available students for a classroom (not enrolled in any class for current academic year).
     *
     * @return Collection
     */
    public function getAvailableStudents(int $academicYearId)
    {
        return Student::whereDoesntHave('classrooms', function ($query) use ($academicYearId) {
            $query->where('classroom_students.academic_year_id', $academicYearId);
        })
            ->whereHas('user', function ($query) {
                $query->where('status', 'active');
            })
            ->with('user')
            ->orderBy('nis')
            ->get();
    }

    /**
     * Get classroom schedule.
     *
     * @return Collection
     */
    public function getClassroomSchedule(int $classroomId)
    {
        $classroom = $this->findById($classroomId);

        return $classroom->schedules()->with(['subject', 'teacher.user'])->get();
    }

    /**
     * Get total count of classrooms.
     *
     * @return int The total number of classrooms
     */
    public function count(): int
    {
        return Classroom::count();
    }

    /**
     * Get count of active classrooms.
     *
     * @return int The number of active classrooms
     */
    public function getActiveCount(): int
    {
        return Classroom::where('status', 'active')->count();
    }

    /**
     * Remove a student from a classroom.
     */
    public function removeStudentFromClassroom(int $classroomId, int $studentId): bool
    {
        DB::beginTransaction();
        try {
            $classroom = $this->findById($classroomId);
            $result = $classroom->students()
                ->wherePivot('academic_year_id', $classroom->academic_year_id)
                ->detach($studentId);
            DB::commit();

            return (bool) $result;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Remove student from classroom failed: '.$e->getMessage());
            throw new \Exception('Gagal mengeluarkan siswa dari kelas: '.$e->getMessage());
        }
    }

    /**
     * Enroll multiple students to a classroom.
     */
    public function enrollStudentsToClassroom(int $classroomId, array $studentIds): bool
    {
        DB::beginTransaction();
        try {
            $classroom = $this->findById($classroomId);
            $academicYearId = $classroom->academic_year_id;

            // Check classroom capacity
            $availableCapacity = $classroom->available_capacity;
            $studentCount = count($studentIds);

            if ($studentCount > $availableCapacity) {
                throw new \Exception("Kapasitas kelas tidak mencukupi. Tersedia: {$availableCapacity}, Diperlukan: {$studentCount}");
            }

            foreach ($studentIds as $studentId) {
                // Check if student is already enrolled in this academic year
                if (! $this->isStudentEnrolledInAcademicYear($studentId, $academicYearId)) {
                    $classroom->students()->attach($studentId, [
                        'academic_year_id' => $academicYearId,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }

            DB::commit();

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Enroll students to classroom failed: '.$e->getMessage());
            throw new \Exception('Gagal mendaftarkan siswa ke kelas: '.$e->getMessage());
        }
    }

    /**
     * Check if a student is already enrolled in any classroom for a specific academic year.
     */
    public function isStudentEnrolledInAcademicYear(int $studentId, int $academicYearId): bool
    {
        return DB::table('classroom_students')
            ->where('student_id', $studentId)
            ->where('academic_year_id', $academicYearId)
            ->exists();
    }

    /**
     * Delete a classroom.
     */
    public function deleteClassroom(int $id): bool
    {
        DB::beginTransaction();
        try {
            $classroom = $this->findById($id);

            // Check if classroom has students
            if ($classroom->students()->count() > 0) {
                throw new \Exception('Kelas tidak dapat dihapus karena masih memiliki siswa.');
            }

            $result = $classroom->delete();
            DB::commit();

            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Delete classroom failed: '.$e->getMessage());
            throw new \Exception('Gagal menghapus kelas: '.$e->getMessage());
        }
    }
}
