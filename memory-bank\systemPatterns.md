# System Patterns: Rawooh School Management System

## Architecture Overview

Rawooh is built on the Laravel PHP framework, following a layered architecture pattern that separates concerns and promotes maintainability and testability.

## Design Patterns

### Repository Pattern

The system extensively implements the Repository Pattern to abstract data access and provide a clean separation between business logic and data access:

-   **Interface Definitions**: Located in `app/Contracts/Interfaces/*`
-   **Repository Implementations**: Located in `app/Repositories/*`
-   **Repository Usage**: Injected into services via dependency injection

This pattern allows for:

-   Decoupling business logic from data access logic
-   Improved testability with repository mocking
-   Consistent data access patterns across the application
-   Future flexibility to change data sources without impacting business logic

### Service Layer Pattern

Business logic is encapsulated in service classes that act as intermediaries between controllers and repositories:

-   **Service Interfaces**: Located in `app/Contracts/Services/*`
-   **Service Implementations**: Located in `app/Services/*`

Benefits of this pattern include:

-   Thin controllers focused on request handling
-   Reusable business logic
-   Improved separation of concerns
-   Easier unit testing

### MVC Pattern

The application follows <PERSON><PERSON>'s implementation of the Model-View-Controller pattern:

-   **Models**: Domain entities in `app/Models/*`
-   **Views**: Blade templates in `resources/views/*`
-   **Controllers**: HTTP request handlers in `app/Http/Controllers/*`

### Traits for Cross-Cutting Concerns

Reusable functionality is implemented using PHP traits:

-   `ApiResponseTrait`: Standardizes API response formats
-   `DatabaseTransactionTrait`: Manages database transactions
-   `DetectsChanges`: Tracks model attribute changes

### Enum Pattern

PHP 8.1+ Enums are used for representing fixed sets of values:

-   Academic year statuses
-   Academic semesters
-   Attendance statuses
-   Various other status types

## Component Relationships

```mermaid
flowchart TD
    Client[Client] --> |HTTP Request| Controllers

    Controllers --> |Uses| Services
    Services --> |Implements| ServiceInterfaces[Service Interfaces]
    Services --> |Uses| Repositories

    Repositories --> |Implements| RepositoryInterfaces[Repository Interfaces]
    Repositories --> |Uses| Models

    Models --> |Query| Database[(Database)]
```

## Key Technical Decisions

1. **Repository Pattern**: Chosen to decouple data access from business logic and improve testability
2. **Service Layer**: Added to centralize business logic and keep controllers thin
3. **PHP Enums**: Used to represent constants and reduce magic strings/numbers
4. **Form Requests**: Laravel form request classes for input validation
5. **Resource Classes**: API resources for consistent data transformation
6. **Trait Composition**: For sharing functionality across classes without inheritance
7. **Exception Handling**: Custom exceptions for domain-specific error cases

## Data Flow Architecture

```mermaid
flowchart LR
    Request[HTTP Request] --> |1| Controllers
    Controllers --> |2| Validation[Form Requests]
    Controllers --> |3| Services
    Services --> |4| Repositories
    Repositories --> |5| Models
    Models --> |6| Database[(Database)]
    Database --> |7| Models
    Models --> |8| Repositories
    Repositories --> |9| Services
    Services --> |10| Resources[API Resources]
    Resources --> |11| Response[HTTP Response]
```

## Authorization Structure

The system uses Spatie's Laravel Permission package for role-based access control:

-   Roles are assigned to users
-   Permissions are grouped by functional areas
-   Controllers check permissions before processing requests
