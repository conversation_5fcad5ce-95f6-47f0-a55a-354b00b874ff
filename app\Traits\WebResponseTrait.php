<?php

namespace App\Traits;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Session;

trait WebResponseTrait
{
    /**
     * Return a success response with a redirect.
     */
    protected function successRedirect(
        string $message,
        string $redirectTo,
        array $data = []
    ): RedirectResponse {
        Session::flash('success', $message);

        if (! empty($data)) {
            Session::flash('data', $data);
        }

        return redirect()->to($redirectTo);
    }

    /**
     * Return a success response with a redirect back.
     */
    protected function successRedirectBack(
        string $message,
        array $data = []
    ): RedirectResponse {
        Session::flash('success', $message);

        if (! empty($data)) {
            Session::flash('data', $data);
        }

        return redirect()->back();
    }

    /**
     * Return an error response with a redirect.
     */
    protected function errorRedirect(
        string $message,
        string $redirectTo,
        array $errors = []
    ): RedirectResponse {
        Session::flash('error', $message);

        if (! empty($errors)) {
            Session::flash('errors', $errors);
        }

        return redirect()->to($redirectTo);
    }

    /**
     * Return an error response with a redirect back.
     */
    protected function errorRedirectBack(
        string $message,
        array $errors = []
    ): RedirectResponse {
        Session::flash('error', $message);

        if (! empty($errors)) {
            Session::flash('errors', $errors);
        }

        return redirect()->back()->withInput();
    }

    /**
     * Return a validation error response with a redirect back.
     */
    protected function validationErrorRedirectBack(
        array $errors,
        string $message = 'Validation Error'
    ): RedirectResponse {
        return $this->errorRedirectBack($message, $errors);
    }
}
