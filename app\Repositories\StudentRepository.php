<?php

namespace App\Repositories;

use App\Contracts\Interfaces\StudentRepositoryInterface;
use App\Contracts\Interfaces\UserRepositoryInterface;
use App\Models\Classroom;
use App\Models\Student;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class StudentRepository implements StudentRepositoryInterface
{
    /**
     * Student model instance
     */
    private Student $studentModel;

    /**
     * User repository instance
     */
    private UserRepositoryInterface $userRepository;

    /**
     * StudentRepository constructor.
     */
    public function __construct(Student $studentModel, UserRepositoryInterface $userRepository)
    {
        $this->studentModel = $studentModel;
        $this->userRepository = $userRepository;
    }

    /**
     * Get all students with optional filters
     */
    public function getAll(array $filters = []): Collection
    {
        $query = $this->studentModel->with('user');

        if (isset($filters['status'])) {
            $query->whereHas('user', function ($query) use ($filters) {
                $query->where('status', $filters['status']);
            });
        }

        if (isset($filters['gender'])) {
            $query->where('gender', $filters['gender']);
        }

        if (isset($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                // Search in the user relationship for name and email
                $q->whereHas('user', function ($userQuery) use ($filters) {
                    $userQuery->where('name', 'like', '%' . $filters['search'] . '%')
                        ->orWhere('email', 'like', '%' . $filters['search'] . '%');
                })
                    // Also search in student-specific fields
                    ->orWhere('nis', 'like', '%' . $filters['search'] . '%')
                    ->orWhere('nisn', 'like', '%' . $filters['search'] . '%');
            });
        }

        return $query->get();
    }

    /**
     * Get all active students
     */
    public function getAllActive(): Collection
    {
        return $this->studentModel->with('user')
            ->whereHas('user', function ($query) {
                $query->where('status', 'active');
            })
            ->get();
    }

    /**
     * Find a student by ID
     *
     * @throws ModelNotFoundException
     */
    public function findById(int $id): Student
    {
        return $this->studentModel->with('user')->findOrFail($id);
    }

    /**
     * Get a student by ID
     */
    public function getById(int $id): ?Student
    {
        try {
            return $this->studentModel->with('user')->findOrFail($id);
        } catch (ModelNotFoundException $e) {
            return null;
        }
    }

    /**
     * Create a new student
     */
    public function create(array $data): Student
    {
        $student = $this->studentModel->newInstance($data);
        $student->save();

        return $student;
    }

    /**
     * Update an existing student
     *
     * @throws ModelNotFoundException
     */
    public function update(int $id, array $data): bool
    {
        $student = $this->findById($id);
        return $student->update($data);
    }

    /**
     * Delete a student
     *
     * @throws ModelNotFoundException
     */
    public function delete(int $id): bool
    {
        $student = $this->findById($id);
        return $student->delete();
    }

    /**
     * Enroll student to a classroom for a specific academic year
     *
     * @throws ModelNotFoundException
     */
    public function enrollToClassroom(int $studentId, int $classroomId, int $academicYearId): bool
    {
        $student = $this->findById($studentId);

        $student->classrooms()->attach($classroomId, [
            'academic_year_id' => $academicYearId,
        ]);

        return true;
    }

    /**
     * Remove student from a classroom
     *
     * @throws ModelNotFoundException
     */
    public function removeFromClassroom(int $studentId, int $classroomId, int $academicYearId): bool
    {
        $student = $this->findById($studentId);

        $student->classrooms()->wherePivot('classroom_id', $classroomId)
            ->wherePivot('academic_year_id', $academicYearId)
            ->detach();

        return true;
    }

    /**
     * Get student's classrooms
     *
     * @throws ModelNotFoundException
     */
    public function getStudentClassrooms(int $studentId): Collection
    {
        $student = $this->findById($studentId);
        return $student->classrooms()->with('academicYear')->get();
    }

    /**
     * Check if student is enrolled in a classroom for a specific academic year
     *
     * @throws ModelNotFoundException
     */
    public function isEnrolledInClassroom(int $studentId, int $classroomId, int $academicYearId): bool
    {
        $student = $this->findById($studentId);

        return $student->classrooms()
            ->wherePivot('classroom_id', $classroomId)
            ->wherePivot('academic_year_id', $academicYearId)
            ->exists();
    }

    /**
     * Get student's academic performance data
     */
    public function getStudentPerformance(int $studentId): array
    {
        // Find the student first to validate it exists
        $this->findById($studentId);

        // This is a placeholder - in a real app, this might query attendance, grades, etc.
        return [
            'student_id' => $studentId,
            'attendance_rate' => 0, // Placeholder
            'average_grade' => 0,   // Placeholder
            'completed_assignments' => 0, // Placeholder
        ];
    }

    /**
     * Get all students with relations
     */
    public function getAllWithRelations(array $relations, array $filters = []): Collection
    {
        $query = $this->studentModel->with($relations);

        if (isset($filters['status'])) {
            $query->whereHas('user', function ($query) use ($filters) {
                $query->where('status', $filters['status']);
            });
        }

        if (isset($filters['classroom_id'])) {
            $query->whereHas('classrooms', function ($query) use ($filters) {
                $query->where('classroom_id', $filters['classroom_id']);
            });
        }

        if (isset($filters['gender'])) {
            $query->where('gender', $filters['gender']);
        }

        if (isset($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                // Search in the user relationship for name and email
                $q->whereHas('user', function ($userQuery) use ($filters) {
                    $userQuery->where('name', 'like', '%' . $filters['search'] . '%')
                        ->orWhere('email', 'like', '%' . $filters['search'] . '%');
                })
                    // Also search in student-specific fields
                    ->orWhere('nis', 'like', '%' . $filters['search'] . '%')
                    ->orWhere('nisn', 'like', '%' . $filters['search'] . '%');
            });
        }

        return $query->get();
    }

    /**
     * Get student by user ID
     */
    public function getByUserId(int $userId): ?Student
    {
        return $this->studentModel->where('user_id', $userId)->first();
    }

    /**
     * Get total count of students
     */
    public function count(): int
    {
        return $this->studentModel->count();
    }

    /**
     * Create a new student with user account
     *
     * @deprecated This method is deprecated and will be removed. Use StudentRegistrationService instead.
     * @throws \Exception
     */
    public function createStudentWithUser(array $data): Student
    {
        throw new \RuntimeException('This method is deprecated. Use StudentRegistrationService::registerStudent() instead.');
    }

    /**
     * Update student and user data
     *
     * @deprecated This method is deprecated and will be removed. Use StudentRegistrationService instead.
     * @throws \Exception
     */
    public function updateStudentWithUser(int $id, array $data): bool
    {
        throw new \RuntimeException('This method is deprecated. Use StudentRegistrationService::updateStudent() instead.');
    }
}


