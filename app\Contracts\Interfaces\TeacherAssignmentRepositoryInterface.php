<?php

namespace App\Contracts\Interfaces;

interface TeacherAssignmentRepositoryInterface
{
    public function getAllTeacherAssignments(array $filters);

    public function getTeacherAssignmentById(int $id);

    public function createTeacherAssignment(array $data);

    public function updateTeacherAssignment(int $id, array $data);

    public function deleteTeacherAssignment(int $id);

    public function getTeacherAssignmentsByTeacherId(int $teacherId, array $filters = []);

    public function getTeacherAssignmentsByClassroomId(int $classroomId, array $filters = []);

    public function getHomeroomTeacherByClassroomId(int $classroomId, int $academicYearId);

    public function hasHomeroomTeacher(int $classroomId, int $academicYearId, ?int $excludeId = null): bool;

    public function hasDuplicateAssignment(array $data, ?int $excludeId = null): bool;

    public function hasActiveClassSchedules(int $id): bool;
}