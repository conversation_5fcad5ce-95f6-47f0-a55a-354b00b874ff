<?php

namespace App\Services;

use App\Contracts\Interfaces\UserRepositoryInterface;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Arr;

class EmployeeService
{
    protected UserRepositoryInterface $userRepository;

    public function __construct(UserRepositoryInterface $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    public function getUsersByRole(string $roleName)
    {
        return $this->userRepository->getUsersByRole($roleName);
    }

    public function getUserById(int $userId)
    {
        return $this->userRepository->getUserById($userId);
    }

    public function deleteUser(int $userId)
    {
        return $this->userRepository->deleteUser($userId);
    }

    public function createStudent(array $data)
    {
        $data['password'] = Hash::make($data['password']);
        $student = $this->userRepository->createUser($data);
        $student->assignRole('student');
        return $student;
    }

    public function createTeacher(array $data)
    {
        $data['password'] = Hash::make($data['password']);
        $teacher = $this->userRepository->createUser($data);
        $teacher->assignRole('teacher');
        return $teacher;
    }

    public function updateUser(User $user, array $data)
    {
        if (!empty($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        } else {
            $data = Arr::except($data, ['password']);
        }

        $this->userRepository->updateUser($user->id, $data);

        // Jika ada input role, sinkronkan. Jika tidak, lewati.
        if (!empty($data['roles'])) {
            $user->syncRoles($data['roles']);
        }

        return $user->fresh();
    }
}
