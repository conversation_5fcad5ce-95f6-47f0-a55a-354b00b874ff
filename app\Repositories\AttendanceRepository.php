<?php

namespace App\Repositories;

use App\Contracts\Interfaces\AttendanceRepositoryInterface;
use App\Enums\AttendanceStatusEnum;
use App\Enums\AttendantTypeEnum;
use App\Exceptions\BusinessLogicException;
use App\Exceptions\DatabaseException;
use App\Exceptions\NotFoundException;
use App\Helpers\ErrorHandler;
use App\Models\Attendance;
use App\Models\Classroom;
use App\Models\ClassSchedule;
use App\Models\Student;
use App\Models\Teacher;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Throwable;
use Yajra\DataTables\Facades\DataTables;

final class AttendanceRepository implements AttendanceRepositoryInterface
{
    private Attendance $attendanceModel;

    /**
     * AttendanceRepository constructor.
     */
    public function __construct(Attendance $attendanceModel)
    {
        $this->attendanceModel = $attendanceModel;
    }

    /**
     * Get all attendances with optional filtering
     *
     * @return Collection|LengthAwarePaginator
     */
    public function getAll(array $filters = [])
    {
        $query = $this->attendanceModel->query();

        // Apply filters
        if (!empty($filters['class_schedule_id'])) {
            $query->where('class_schedule_id', $filters['class_schedule_id']);
        }

        if (!empty($filters['attendant_type'])) {
            $query->where('attendant_type', $filters['attendant_type']);
        }

        if (!empty($filters['attendance_status'])) {
            $query->where('attendance_status', $filters['attendance_status']);
        }

        if (!empty($filters['date_from']) && !empty($filters['date_to'])) {
            $query->whereBetween('attendance_date', [$filters['date_from'], $filters['date_to']]);
        } elseif (!empty($filters['date_from'])) {
            $query->where('attendance_date', '>=', $filters['date_from']);
        } elseif (!empty($filters['date_to'])) {
            $query->where('attendance_date', '<=', $filters['date_to']);
        }

        if (!empty($filters['student_id'])) {
            $query->where('student_id', $filters['student_id']);
        }

        if (!empty($filters['teacher_id'])) {
            $query->where('teacher_id', $filters['teacher_id']);
        }

        // Order by
        $query->orderBy('attendance_date', 'desc');

        // Pagination if needed
        if (!empty($filters['paginate'])) {
            return $query->paginate($filters['per_page'] ?? 15);
        }

        return $query->get();
    }

    /**
     * Get all attendances with relations
     *
     * @return Collection|LengthAwarePaginator
     */
    public function getAllWithRelations(array $relations = [], array $filters = [])
    {
        $query = $this->attendanceModel->with($relations);

        // Apply filters
        if (!empty($filters['class_schedule_id'])) {
            $query->where('class_schedule_id', $filters['class_schedule_id']);
        }

        if (!empty($filters['attendant_type'])) {
            $query->where('attendant_type', $filters['attendant_type']);
        }

        if (!empty($filters['attendance_status'])) {
            $query->where('attendance_status', $filters['attendance_status']);
        }

        if (!empty($filters['date_from']) && !empty($filters['date_to'])) {
            $query->whereBetween('attendance_date', [$filters['date_from'], $filters['date_to']]);
        } elseif (!empty($filters['date_from'])) {
            $query->where('attendance_date', '>=', $filters['date_from']);
        } elseif (!empty($filters['date_to'])) {
            $query->where('attendance_date', '<=', $filters['date_to']);
        }

        if (!empty($filters['student_id'])) {
            $query->where('student_id', $filters['student_id']);
        }

        if (!empty($filters['teacher_id'])) {
            $query->where('teacher_id', $filters['teacher_id']);
        }

        // For DataTables
        if (!empty($filters['datatable'])) {
            return DataTables::of($query)
                ->addIndexColumn()
                ->editColumn('attendance_date', function ($row) {
                    return $row->attendance_date->format('d-m-Y H:i');
                })
                ->editColumn('attendance_status', function ($row) {
                    $status = $row->attendance_status;
                    $color = $status->color();
                    $label = AttendanceStatusEnum::getLabel($status);

                    return "<span class='badge bg-{$color}'>{$label}</span>";
                })
                ->editColumn('attendant_type', function ($row) {
                    return AttendantTypeEnum::getLabel($row->attendant_type);
                })
                ->addColumn('student_name', function ($row) {
                    return $row->student ? $row->student->user->name : '-';
                })
                ->addColumn('teacher_name', function ($row) {
                    return $row->teacher ? $row->teacher->user->name : '-';
                })
                ->addColumn('class_name', function ($row) {
                    return $row->classSchedule ? $row->classSchedule->classroom->name : '-';
                })
                ->addColumn('subject_name', function ($row) {
                    return $row->classSchedule ? $row->classSchedule->subject->name : '-';
                })
                ->addColumn('action', function ($row) {
                    return view('admin.components.attendance-actions', [
                        'id' => $row->id,
                        'editUrl' => route('admin.attendances.edit', $row->id),
                        'viewUrl' => route('admin.attendances.show', $row->id),
                        'deleteUrl' => route('admin.attendances.destroy', $row->id),
                    ]);
                })
                ->rawColumns(['action', 'attendance_status'])
                ->make(true);
        }

        // Order by
        $query->orderBy('attendance_date', 'desc');

        // Pagination if needed
        if (!empty($filters['paginate'])) {
            return $query->paginate($filters['per_page'] ?? 15);
        }

        return $query->get();
    }

    /**
     * Find attendance by ID
     *
     * @throws NotFoundException
     */
    public function findById(int $id): Attendance
    {
        try {
            return $this->attendanceModel->with(['classSchedule', 'student.user', 'teacher.user'])->findOrFail($id);
        } catch (Throwable $e) {
            ErrorHandler::notFound("Kehadiran dengan ID {$id} tidak ditemukan");

            // This line will never be executed because ErrorHandler::notFound throws an exception
            return $this->attendanceModel->newInstance();
        }
    }

    /**
     * Record a new attendance
     *
     * @throws BusinessLogicException
     * @throws DatabaseException
     */
    public function recordAttendance(array $data): Attendance
    {
        DB::beginTransaction();
        try {
            // Validate required data
            if (
                empty($data['class_schedule_id']) ||
                empty($data['attendance_date']) ||
                empty($data['attendance_status']) ||
                empty($data['attendant_type'])
            ) {
                ErrorHandler::businessLogic('Semua data wajib harus diisi');
            }

            // Check if class schedule exists
            $classSchedule = ClassSchedule::find($data['class_schedule_id']);
            if (!$classSchedule) {
                ErrorHandler::businessLogic('Jadwal kelas tidak ditemukan');
            }

            // Validate student or teacher based on attendant type
            if ($data['attendant_type'] === AttendantTypeEnum::STUDENT->value) {
                if (empty($data['student_id'])) {
                    ErrorHandler::businessLogic('ID siswa harus diisi untuk kehadiran siswa');
                }

                // Check if student exists
                $student = Student::find($data['student_id']);
                if (!$student) {
                    ErrorHandler::businessLogic('Siswa tidak ditemukan');
                }

                // Check if student is in the class
                $isStudentInClass = $classSchedule->classroom->students()
                    ->wherePivot('student_id', $data['student_id'])
                    ->exists();

                if (!$isStudentInClass) {
                    ErrorHandler::businessLogic('Siswa tidak terdaftar di kelas ini');
                }
            } elseif ($data['attendant_type'] === AttendantTypeEnum::TEACHER->value) {
                if (empty($data['teacher_id'])) {
                    ErrorHandler::businessLogic('ID guru harus diisi untuk kehadiran guru');
                }

                // Check if teacher exists
                $teacher = Teacher::find($data['teacher_id']);
                if (!$teacher) {
                    ErrorHandler::businessLogic('Guru tidak ditemukan');
                }

                // Check if teacher is assigned to the class schedule
                if ($classSchedule->teacherAssignment->teacher_id != $data['teacher_id']) {
                    ErrorHandler::businessLogic('Guru tidak ditugaskan untuk jadwal kelas ini');
                }
            } else {
                ErrorHandler::businessLogic('Tipe kehadiran tidak valid');
            }

            // Check for duplicate attendance
            $existingAttendance = $this->attendanceModel
                ->where('class_schedule_id', $data['class_schedule_id'])
                ->where('attendance_date', Carbon::parse($data['attendance_date'])->format('Y-m-d'))
                ->where('attendant_type', $data['attendant_type']);

            if ($data['attendant_type'] === AttendantTypeEnum::STUDENT->value) {
                $existingAttendance->where('student_id', $data['student_id']);
            } else {
                $existingAttendance->where('teacher_id', $data['teacher_id']);
            }

            if ($existingAttendance->exists()) {
                ErrorHandler::businessLogic('Kehadiran untuk tanggal ini sudah tercatat');
            }

            // Create attendance record
            $attendance = new Attendance;
            $attendance->class_schedule_id = $data['class_schedule_id'];
            $attendance->attendance_date = $data['attendance_date'];
            $attendance->attendance_status = $data['attendance_status'];
            $attendance->attendant_type = $data['attendant_type'];
            $attendance->notes = $data['notes'] ?? null;
            $attendance->longitude = $data['longitude'] ?? null;
            $attendance->latitude = $data['latitude'] ?? null;

            if ($data['attendant_type'] === AttendantTypeEnum::STUDENT->value) {
                $attendance->student_id = $data['student_id'];
            } else {
                $attendance->teacher_id = $data['teacher_id'];
            }

            $attendance->save();

            DB::commit();

            return $attendance;
        } catch (BusinessLogicException $e) {
            DB::rollBack();
            throw $e;
        } catch (Throwable $e) {
            DB::rollBack();
            ErrorHandler::database('Gagal menyimpan data kehadiran: ' . $e->getMessage(), $e);

            // This line will never be executed because ErrorHandler::database throws an exception
            return $this->attendanceModel->newInstance();
        }
    }

    /**
     * Update an existing attendance
     *
     * @throws BusinessLogicException
     * @throws DatabaseException
     * @throws NotFoundException
     */
    public function updateAttendance(int $id, array $data): bool
    {
        DB::beginTransaction();
        try {
            // Find the attendance record
            $attendance = $this->findById($id);

            // Update fields
            if (isset($data['attendance_status'])) {
                $attendance->attendance_status = $data['attendance_status'];
            }

            if (isset($data['notes'])) {
                $attendance->notes = $data['notes'];
            }

            if (isset($data['longitude'])) {
                $attendance->longitude = $data['longitude'];
            }

            if (isset($data['latitude'])) {
                $attendance->latitude = $data['latitude'];
            }

            // Save changes
            $result = $attendance->save();

            DB::commit();

            return $result;
        } catch (NotFoundException $e) {
            DB::rollBack();
            throw $e;
        } catch (BusinessLogicException $e) {
            DB::rollBack();
            throw $e;
        } catch (Throwable $e) {
            DB::rollBack();
            ErrorHandler::database('Gagal memperbarui data kehadiran: ' . $e->getMessage(), $e);

            return false;
        }
    }

    /**
     * Delete an attendance
     *
     * @throws DatabaseException
     * @throws NotFoundException
     */
    public function delete(int $id): bool
    {
        DB::beginTransaction();
        try {
            // Find the attendance record
            $attendance = $this->findById($id);

            // Delete the record
            $result = $attendance->delete();

            DB::commit();

            return $result;
        } catch (NotFoundException $e) {
            DB::rollBack();
            throw $e;
        } catch (Throwable $e) {
            DB::rollBack();
            ErrorHandler::database('Gagal menghapus data kehadiran: ' . $e->getMessage(), $e);

            return false;
        }
    }

    /**
     * Get class attendance for a specific date
     */
    public function getClassAttendance(int $classScheduleId, string $date): Collection
    {
        $dateObj = Carbon::parse($date);
        $startDate = $dateObj->startOfDay()->format('Y-m-d H:i:s');
        $endDate = $dateObj->endOfDay()->format('Y-m-d H:i:s');

        return $this->attendanceModel
            ->where('class_schedule_id', $classScheduleId)
            ->whereBetween('attendance_date', [$startDate, $endDate])
            ->with(['student.user', 'teacher.user'])
            ->get();
    }

    /**
     * Get student attendance with filters
     */
    public function getStudentAttendance(int $studentId, array $filters = []): Collection
    {
        $query = $this->attendanceModel
            ->where('student_id', $studentId)
            ->where('attendant_type', AttendantTypeEnum::STUDENT->value);

        // Apply date filters
        if (!empty($filters['date_from']) && !empty($filters['date_to'])) {
            $query->whereBetween('attendance_date', [$filters['date_from'], $filters['date_to']]);
        } elseif (!empty($filters['date_from'])) {
            $query->where('attendance_date', '>=', $filters['date_from']);
        } elseif (!empty($filters['date_to'])) {
            $query->where('attendance_date', '<=', $filters['date_to']);
        }

        // Apply status filter
        if (!empty($filters['attendance_status'])) {
            $query->where('attendance_status', $filters['attendance_status']);
        }

        // Apply class schedule filter
        if (!empty($filters['class_schedule_id'])) {
            $query->where('class_schedule_id', $filters['class_schedule_id']);
        }

        return $query->with(['classSchedule.classroom', 'classSchedule.subject'])
            ->orderBy('attendance_date', 'desc')
            ->get();
    }

    /**
     * Get teacher attendance with filters
     */
    public function getTeacherAttendance(int $teacherId, array $filters = []): Collection
    {
        $query = $this->attendanceModel
            ->where('teacher_id', $teacherId)
            ->where('attendant_type', AttendantTypeEnum::TEACHER->value);

        // Apply date filters
        if (!empty($filters['date_from']) && !empty($filters['date_to'])) {
            $query->whereBetween('attendance_date', [$filters['date_from'], $filters['date_to']]);
        } elseif (!empty($filters['date_from'])) {
            $query->where('attendance_date', '>=', $filters['date_from']);
        } elseif (!empty($filters['date_to'])) {
            $query->where('attendance_date', '<=', $filters['date_to']);
        }

        // Apply status filter
        if (!empty($filters['attendance_status'])) {
            $query->where('attendance_status', $filters['attendance_status']);
        }

        // Apply class schedule filter
        if (!empty($filters['class_schedule_id'])) {
            $query->where('class_schedule_id', $filters['class_schedule_id']);
        }

        return $query->with(['classSchedule.classroom', 'classSchedule.subject'])
            ->orderBy('attendance_date', 'desc')
            ->get();
    }

    /**
     * Get attendance summary for a classroom in a specific month
     *
     * @param  string  $month  Format: YYYY-MM
     */
    public function getAttendanceSummary(int $classroomId, string $month): array
    {
        // Parse the month
        $date = Carbon::createFromFormat('Y-m', $month);
        $startDate = $date->copy()->startOfMonth()->format('Y-m-d');
        $endDate = $date->copy()->endOfMonth()->format('Y-m-d');

        // Get the classroom
        $classroom = Classroom::findOrFail($classroomId);

        // Get all students in the classroom
        $students = $classroom->students;

        // Get all class schedules for the classroom
        $classScheduleIds = ClassSchedule::whereHas('teacherAssignment', function ($query) use ($classroomId) {
            $query->whereHas('classroom', function ($q) use ($classroomId) {
                $q->where('id', $classroomId);
            });
        })->pluck('id');

        // Get all attendance records for the classroom in the specified month
        $attendances = $this->attendanceModel
            ->whereIn('class_schedule_id', $classScheduleIds)
            ->where('attendant_type', AttendantTypeEnum::STUDENT->value)
            ->whereBetween('attendance_date', [$startDate, $endDate])
            ->with(['student.user'])
            ->get();

        // Prepare summary data
        $summary = [
            'classroom' => $classroom->name,
            'month' => $date->format('F Y'),
            'total_students' => $students->count(),
            'total_days' => $date->daysInMonth,
            'attendance_by_status' => [
                AttendanceStatusEnum::PRESENT->value => $attendances->where('attendance_status', AttendanceStatusEnum::PRESENT->value)->count(),
                AttendanceStatusEnum::ABSENT->value => $attendances->where('attendance_status', AttendanceStatusEnum::ABSENT->value)->count(),
                AttendanceStatusEnum::LATE->value => $attendances->where('attendance_status', AttendanceStatusEnum::LATE->value)->count(),
            ],
            'students' => [],
        ];

        // Calculate attendance for each student
        foreach ($students as $student) {
            $studentAttendances = $attendances->where('student_id', $student->id);

            $summary['students'][] = [
                'id' => $student->id,
                'name' => $student->user->name,
                'present_count' => $studentAttendances->where('attendance_status', AttendanceStatusEnum::PRESENT->value)->count(),
                'absent_count' => $studentAttendances->where('attendance_status', AttendanceStatusEnum::ABSENT->value)->count(),
                'late_count' => $studentAttendances->where('attendance_status', AttendanceStatusEnum::LATE->value)->count(),
                'attendance_percentage' => $studentAttendances->count() > 0
                    ? round(($studentAttendances->where('attendance_status', AttendanceStatusEnum::PRESENT->value)->count() / $studentAttendances->count()) * 100, 2)
                    : 0,
            ];
        }

        return $summary;
    }
}
