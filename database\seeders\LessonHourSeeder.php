<?php

namespace Database\Seeders;

use App\Models\LessonHour;
use App\Models\Shift;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LessonHourSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ensure shifts exist
        $this->command->info('Checking if shifts exist...');
        $morningShift = Shift::where('name', 'Pagi')->first();
        $afternoonShift = Shift::where('name', 'Siang')->first();

        if (! $morningShift || ! $afternoonShift) {
            $this->command->info('Shifts not found. Please run ShiftSeeder first.');
            $this->command->call('db:seed', ['--class' => 'ShiftSeeder']);
            $morningShift = Shift::where('name', 'Pagi')->first();
            $afternoonShift = Shift::where('name', 'Siang')->first();
        }

        // Clear existing lesson hours
        $this->command->info('Clearing existing lesson hours...');
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        LessonHour::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // Morning shift lesson hours
        $morningLessonHours = [
            ['name' => 'Jam ke-1', 'start_time' => '07:00:00', 'end_time' => '07:45:00', 'sequence' => 1, 'shift_id' => $morningShift->id],
            ['name' => 'Jam ke-2', 'start_time' => '07:45:00', 'end_time' => '08:30:00', 'sequence' => 2, 'shift_id' => $morningShift->id],
            ['name' => 'Jam ke-3', 'start_time' => '08:30:00', 'end_time' => '09:15:00', 'sequence' => 3, 'shift_id' => $morningShift->id],
            ['name' => 'Istirahat 1', 'start_time' => '09:15:00', 'end_time' => '09:30:00', 'sequence' => 4, 'shift_id' => $morningShift->id],
            ['name' => 'Jam ke-4', 'start_time' => '09:30:00', 'end_time' => '10:15:00', 'sequence' => 5, 'shift_id' => $morningShift->id],
            ['name' => 'Jam ke-5', 'start_time' => '10:15:00', 'end_time' => '11:00:00', 'sequence' => 6, 'shift_id' => $morningShift->id],
            ['name' => 'Jam ke-6', 'start_time' => '11:00:00', 'end_time' => '11:45:00', 'sequence' => 7, 'shift_id' => $morningShift->id],
            ['name' => 'Istirahat 2', 'start_time' => '11:45:00', 'end_time' => '12:15:00', 'sequence' => 8, 'shift_id' => $morningShift->id],
        ];

        // Afternoon shift lesson hours
        $afternoonLessonHours = [
            ['name' => 'Jam ke-1', 'start_time' => '12:30:00', 'end_time' => '13:15:00', 'sequence' => 1, 'shift_id' => $afternoonShift->id],
            ['name' => 'Jam ke-2', 'start_time' => '13:15:00', 'end_time' => '14:00:00', 'sequence' => 2, 'shift_id' => $afternoonShift->id],
            ['name' => 'Jam ke-3', 'start_time' => '14:00:00', 'end_time' => '14:45:00', 'sequence' => 3, 'shift_id' => $afternoonShift->id],
            ['name' => 'Istirahat 1', 'start_time' => '14:45:00', 'end_time' => '15:00:00', 'sequence' => 4, 'shift_id' => $afternoonShift->id],
            ['name' => 'Jam ke-4', 'start_time' => '15:00:00', 'end_time' => '15:45:00', 'sequence' => 5, 'shift_id' => $afternoonShift->id],
            ['name' => 'Jam ke-5', 'start_time' => '15:45:00', 'end_time' => '16:30:00', 'sequence' => 6, 'shift_id' => $afternoonShift->id],
            ['name' => 'Jam ke-6', 'start_time' => '16:30:00', 'end_time' => '17:00:00', 'sequence' => 7, 'shift_id' => $afternoonShift->id],
        ];

        $this->command->info('Creating morning shift lesson hours...');
        foreach ($morningLessonHours as $lessonHour) {
            LessonHour::create($lessonHour);
        }

        $this->command->info('Creating afternoon shift lesson hours...');
        foreach ($afternoonLessonHours as $lessonHour) {
            LessonHour::create($lessonHour);
        }

        $this->command->info('Lesson hours created successfully!');
    }
}
