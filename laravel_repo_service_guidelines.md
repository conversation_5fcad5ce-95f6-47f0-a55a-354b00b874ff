# Laravel Repository & Service Structure Guidelines

## 🔧 Project Rules Clarification

This document defines the architecture rules and folder structure conventions for Laravel projects under this team.

### ✅ Interface Usage Policy

-   **Interfaces are used ONLY for Repository classes**.
-   **Services DO NOT require interfaces** and should be injected directly.

---

## 📁 Folder Structure Convention

```
app/
├── Repositories/
│   ├── Contracts/
│   │   ├── UserRepositoryInterface.php
│   │   ├── EmployeeRepositoryInterface.php
│   │   └── ...
│   └── Eloquent/
│       ├── UserRepository.php
│       ├── EmployeeRepository.php
│       └── ...
├── Services/
│   ├── UserService.php
│   ├── EmployeeService.php
│   └── ...
```

> Notes:
>
> -   `Contracts/` holds all repository interfaces.
> -   `Eloquent/` contains the Eloquent-based repository implementations.
> -   `Services/` holds all service classes without interfaces.

---

## 🧱 Repository Example

### UserRepositoryInterface.php

```php
namespace App\Repositories\Contracts;

use Illuminate\Support\Collection;

interface UserRepositoryInterface
{
    public function findById(int $id): ?User;
    public function getAll(): Collection;
    public function create(array $data): User;
    public function update(int $id, array $data): bool;
    public function delete(int $id): bool;
}
```

### UserRepository.php

```php
namespace App\Repositories\Eloquent;

use App\Models\User;
use App\Repositories\Contracts\UserRepositoryInterface;
use Illuminate\Support\Collection;

class UserRepository implements UserRepositoryInterface
{
    protected $user;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    public function findById(int $id): ?User
    {
        return $this->user->find($id);
    }

    public function getAll(): Collection
    {
        return $this->user->all();
    }

    public function create(array $data): User
    {
        return $this->user->create($data);
    }

    public function update(int $id, array $data): bool
    {
        $user = $this->user->findOrFail($id);
        return $user->update($data);
    }

    public function delete(int $id): bool
    {
        return $this->user->destroy($id) > 0;
    }
}
```

---

## 🔨 Service Example (No Interface)

### UserService.php

```php
namespace App\Services;

use App\Repositories\Contracts\UserRepositoryInterface;

class UserService
{
    public function __construct(
        protected UserRepositoryInterface $userRepository
    ) {}

    public function registerUser(array $data)
    {
        // Example business logic
        return $this->userRepository->create($data);
    }
}
```

---

## 🧩 Binding Repository Interfaces

### In `App\Providers\AppServiceProvider.php`

```php
use App\Repositories\Contracts\UserRepositoryInterface;
use App\Repositories\Eloquent\UserRepository;
use App\Repositories\Contracts\EmployeeRepositoryInterface;
use App\Repositories\Eloquent\EmployeeRepository;

public function register(): void
{
    $this->app->bind(UserRepositoryInterface::class, UserRepository::class);
    $this->app->bind(EmployeeRepositoryInterface::class, EmployeeRepository::class);
    // Add other repository bindings as needed
}
```

> Services do not need to be bound here unless using a custom resolver.

---
