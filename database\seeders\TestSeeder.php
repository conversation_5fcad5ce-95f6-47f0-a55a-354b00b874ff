<?php

namespace Database\Seeders;

use App\Enums\AcademicSemesterEnum;
use App\Enums\ClassroomLevelEnum;
use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use App\Models\AcademicYear;
use App\Models\Classroom;
use App\Models\Program;
use App\Models\Student;
use App\Models\Teacher;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class TestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::create([
            'username' => 'admin',
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone_number' => '081234567890',
            'status' => UserStatus::Active->value,
            'remember_token' => Str::random(10),
        ]);
        $admin->assignRole(RoleEnum::ADMIN->value);

        // Create academic years
        $currentYear = now()->year;
        $academicYears = [
            AcademicYear::create([
                'name' => ($currentYear - 1).'/'.$currentYear,
                'semester' => AcademicSemesterEnum::ODD->value,
                'start_date' => now()->subMonths(6),
                'end_date' => now()->addMonths(6),
                'status' => 'active',
            ]),
            AcademicYear::create([
                'name' => $currentYear.'/'.($currentYear + 1),
                'semester' => AcademicSemesterEnum::ODD->value,
                'start_date' => now()->addMonths(6),
                'end_date' => now()->addMonths(12),
                'status' => 'planned',
            ]),
        ];

        // Create programs
        $programs = [
            Program::create([
                'name' => 'Program Unggulan',
            ]),
            Program::create([
                'name' => 'Program Reguler',
            ]),
        ];

        // Create teachers
        $teachers = [];
        for ($i = 1; $i <= 10; $i++) {
            $user = User::create([
                'username' => 'teacher'.$i,
                'name' => 'Teacher '.$i,
                'email' => 'teacher'.$i.'@example.com',
                'password' => Hash::make('password'),
                'phone_number' => '0812345678'.$i,
                'status' => UserStatus::Active->value,
                'remember_token' => Str::random(10),
            ]);
            $user->assignRole(RoleEnum::SUBJECT_TEACHER->value);

            $teachers[] = Teacher::create([
                'user_id' => $user->id,
                'birth_place' => 'City '.$i,
                'birth_date' => now()->subYears(30 + $i),
                'gender' => $i % 2 ? 'male' : 'female',
                'phone_number' => $user->phone_number,
                'full_address' => 'Address '.$i,
            ]);
        }

        // Create classrooms for each program and level
        $classrooms = [];
        foreach ($programs as $program) {
            foreach (ClassroomLevelEnum::cases() as $level) {
                for ($i = 1; $i <= 2; $i++) { // 2 classrooms per level
                    $classrooms[] = Classroom::create([
                        'name' => $level->value.' '.$program->code.' '.$i,
                        'level' => $level->value,
                        'capacity' => 30,
                        'program_id' => $program->id,
                        'teacher_id' => $teachers[array_rand($teachers)]->id,
                        'academic_year_id' => $academicYears[0]->id,
                        'status' => 'active',
                    ]);
                }
            }
        }

        // Create students
        $students = [];
        for ($i = 1; $i <= 50; $i++) {
            $user = User::create([
                'username' => 'student'.$i,
                'name' => 'Student '.$i,
                'email' => 'student'.$i.'@example.com',
                'password' => Hash::make('password'),
                'phone_number' => '0812345678'.$i,
                'status' => UserStatus::Active->value,
                'remember_token' => Str::random(10),
            ]);
            $user->assignRole(RoleEnum::STUDENT->value);

            $students[] = Student::create([
                'user_id' => $user->id,
                'nis' => str_pad($i, 8, '0', STR_PAD_LEFT),
                'nisn' => str_pad($i, 10, '0', STR_PAD_LEFT),
                'birth_place' => 'City '.$i,
                'birth_date' => now()->subYears(15 + ($i % 3)),
                'gender' => $i % 2 ? 'male' : 'female',
                'religion' => ['islam', 'kristen', 'katolik', 'hindu', 'buddha', 'konghucu'][$i % 6],
                'phone' => $user->phone_number,
                'address' => 'Address '.$i,
                'parent_name' => 'Parent '.$i,
                'parent_phone' => '0812345678'.($i + 100),
                'parent_occupation' => ['PNS', 'Wiraswasta', 'Guru', 'Dokter', 'Pengusaha'][$i % 5],
                'parent_address' => 'Parent Address '.$i,
                'entry_year' => $currentYear - ($i % 3),
            ]);
        }

        // Enroll students to classrooms
        foreach ($classrooms as $classroom) {
            $enrollmentCount = rand(15, 25); // Random number of students per classroom
            $randomStudents = collect($students)->random($enrollmentCount);

            foreach ($randomStudents as $student) {
                $classroom->students()->attach($student->id, [
                    'academic_year_id' => $academicYears[0]->id,
                ]);
            }
        }

        $this->command->info('Test data seeded successfully!');
    }
}
