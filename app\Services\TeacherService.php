<?php

namespace App\Services;

use Throwable;
use App\Models\Teacher;
use App\Enums\UserStatus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Exceptions\DatabaseException;
use App\Exceptions\NotFoundException;
use App\Exceptions\BusinessLogicException;
use App\Exceptions\TeacherException;
use Illuminate\Database\Eloquent\Collection;
use App\Contracts\Interfaces\UserRepositoryInterface;
use App\Contracts\Interfaces\TeacherRepositoryInterface;

class TeacherService
{
    /**
     * The teacher repository instance.
     */
    protected TeacherRepositoryInterface $teacherRepository;

    /**
     * The user repository instance.
     */
    protected UserRepositoryInterface $userRepository;

    /**
     * Create a new TeacherService instance.
     */
    public function __construct(
        TeacherRepositoryInterface $teacherRepository,
        UserRepositoryInterface $userRepository
    ) {
        $this->teacherRepository = $teacherRepository;
        $this->userRepository = $userRepository;
    }

    /**
     * Get all teachers with optional filtering.
     */
    public function getAll(array $filters = []): Collection
    {
        return $this->teacherRepository->getAll($filters);
    }

    /**
     * Get all teachers (legacy method).
     *
     * @deprecated Use getAll instead
     */
    public function getAllTeachers(array $filters = []): Collection
    {
        return $this->getAll($filters);
    }

    /**
     * Get all active teachers.
     */
    public function getAllActiveTeachers(): Collection
    {
        return $this->teacherRepository->getAllActive();
    }

    /**
     * Find a teacher by ID.
     *
     * @throws \App\Exceptions\NotFoundException
     */
    public function findById(int $id): Teacher
    {
        return $this->teacherRepository->findById($id);
    }

    /**
     * Get a teacher by ID (legacy method).
     *
     * @deprecated Use findById instead
     */
    public function getTeacherById(int $id): Teacher
    {
        return $this->findById($id);
    }

    /**
     * Create a new teacher with user account.
     *
     * @throws \App\Exceptions\BusinessLogicException
     * @throws \App\Exceptions\DatabaseException
     */
    public function create(array $data): Teacher
    {
        try {
            // Validate business rules here before delegating to repository
            $this->validateTeacherData($data);

            return DB::transaction(function () use ($data) {
                return $this->teacherRepository->createTeacherWithUser($data);
            });
        } catch (TeacherException | BusinessLogicException | DatabaseException $e) {
            // Just rethrow these exceptions as they are already properly formatted
            throw $e;
        } catch (Throwable $e) {
            // Convert any other exceptions to DatabaseException
            throw new DatabaseException('Gagal membuat data guru: ' . $e->getMessage(), null, [], [], 0, $e);
        }
    }

    /**
     * Create a new teacher (legacy method).
     *
     * @deprecated Use create instead
     */
    public function createTeacher(array $data): Teacher
    {
        return $this->create($data);
    }

    /**
     * Update teacher data.
     *
     * @throws \App\Exceptions\BusinessLogicException
     * @throws \App\Exceptions\DatabaseException
     * @throws \App\Exceptions\NotFoundException
     */
    public function update(int $id, array $data): bool
    {
        try {
            // Validate business rules here before delegating to repository
            $this->validateTeacherUpdateData($id, $data);

            return DB::transaction(function () use ($id, $data) {
                return $this->teacherRepository->updateTeacherWithUser($id, $data);
            });
        } catch (TeacherException | BusinessLogicException | DatabaseException | NotFoundException $e) {
            // Just rethrow these exceptions as they are already properly formatted
            throw $e;
        } catch (Throwable $e) {
            // Convert any other exceptions to DatabaseException
            throw new DatabaseException('Gagal memperbarui data guru: ' . $e->getMessage(), null, [], [], 0, $e);
        }
    }

    /**
     * Update teacher data (legacy method).
     *
     * @deprecated Use update instead
     */
    public function updateTeacher(int $id, array $data): bool
    {
        return $this->update($id, $data);
    }

    /**
     * Get available teachers with optional filtering.
     */
    public function getAvailableTeachers(array $filters = []): Collection
    {
        return $this->teacherRepository->getAvailableTeachers($filters);
    }

    /**
     * Get teacher assignments.
     */
    public function getTeacherAssignments(int $teacherId): Collection
    {
        return $this->teacherRepository->getTeacherAssignments($teacherId);
    }

    /**
     * Get teacher schedule for a specific date.
     */
    public function getTeacherSchedule(int $teacherId, string $date): Collection
    {
        return $this->teacherRepository->getTeacherSchedule($teacherId, $date);
    }

    /**
     * Get total number of teachers.
     */
    public function getTotalTeachers(): int
    {
        return $this->teacherRepository->count();
    }

    /**
     * Delete a teacher.
     *
     * @throws \App\Exceptions\BusinessLogicException
     * @throws \App\Exceptions\DatabaseException
     * @throws \App\Exceptions\NotFoundException
     */
    public function deleteTeacher(int $id): bool
    {
        try {
            // Business validation - Check if the teacher can be deleted
            $this->validateTeacherDeletion($id);

            return DB::transaction(function () use ($id) {
                return $this->teacherRepository->delete($id);
            });
        } catch (TeacherException | BusinessLogicException | DatabaseException | NotFoundException $e) {
            // Just rethrow these exceptions as they are already properly formatted
            throw $e;
        } catch (Throwable $e) {
            // Convert any other exceptions to DatabaseException
            throw new DatabaseException('Gagal menghapus data guru: ' . $e->getMessage(), null, [], [], 0, $e);
        }
    }

    /**
     * Validate teacher data for creation
     *
     * @throws \App\Exceptions\TeacherException
     */
    private function validateTeacherData(array $data): void
    {
        // Business validation logic for teacher creation
        if (!empty($data['email'])) {
            $existingUser = $this->userRepository->findByEmail($data['email'] ?? '');
            if ($existingUser) {
                throw TeacherException::emailAlreadyExists($data['email']);
            }
        }

        if (!empty($data['username'])) {
            $existingUser = $this->userRepository->findByUsername($data['username'] ?? '');
            if ($existingUser) {
                throw TeacherException::usernameAlreadyExists($data['username']);
            }
        }
    }

    /**
     * Validate teacher data for update
     *
     * @throws \App\Exceptions\TeacherException
     * @throws \App\Exceptions\NotFoundException
     */
    private function validateTeacherUpdateData(int $id, array $data): void
    {
        $teacher = $this->teacherRepository->findById($id);

        // Business validation logic for teacher updates
        if (!empty($data['email']) && $data['email'] !== $teacher->user->email) {
            $existingUser = $this->userRepository->findByEmail($data['email'] ?? '');
            if ($existingUser && $existingUser->id !== $teacher->user_id) {
                throw TeacherException::emailAlreadyExists($data['email']);
            }
        }

        if (!empty($data['username']) && $data['username'] !== $teacher->user->username) {
            $existingUser = $this->userRepository->findByUsername($data['username'] ?? '');
            if ($existingUser && $existingUser->id !== $teacher->user_id) {
                throw TeacherException::usernameAlreadyExists($data['username']);
            }
        }
    }

    /**
     * Validate if a teacher can be deleted
     *
     * @throws \App\Exceptions\TeacherException
     * @throws \App\Exceptions\NotFoundException
     */
    private function validateTeacherDeletion(int $id): void
    {
        $teacher = $this->teacherRepository->findById($id);

        // Check if teacher has assignments - business rule
        if ($this->teacherRepository->getTeacherAssignments($id)->count() > 0) {
            throw TeacherException::cannotDeleteWithAssignments();
        }
    }

    /**
     * Change the status of a teacher's account.
     *
     * @throws TeacherException
     * @throws NotFoundException
     */
    public function changeTeacherStatus(int $id, bool $status): Teacher
    {
        $teacher = $this->teacherRepository->findById($id);

        if ($teacher->user->id === auth()->id()) {
            throw TeacherException::cannotChangeOwnStatus();
        }

        $teacher->user->status = $status ? UserStatus::Active : UserStatus::Inactive;
        $teacher->user->save();

        return $teacher;
    }

    /**
     * Update teacher account settings.
     *
     * @throws BusinessLogicException
     * @throws NotFoundException
     * @throws DatabaseException
     */
    public function updateTeacherAccount(int $id, array $data): Teacher
    {
        try {
            return DB::transaction(function () use ($id, $data) {
                $teacher = $this->teacherRepository->findById($id);
                $user = $teacher->user;

                if (!empty($data['email']) && $data['email'] !== $user->email) {
                    $existingUser = $this->userRepository->findByEmail($data['email']);
                    if ($existingUser && $existingUser->id !== $user->id) {
                        throw new BusinessLogicException('Email sudah digunakan oleh pengguna lain.');
                    }
                }

                $user->name = $data['name'];
                $user->email = $data['email'];

                if (!empty($data['password'])) {
                    $user->password = Hash::make($data['password']);
                }

                if ($user->getRoleNames()->first() !== $data['role']) {
                    $user->syncRoles([$data['role']]);
                }

                $user->save();

                return $teacher;
            });
        } catch (BusinessLogicException | NotFoundException $e) {
            throw $e;
        } catch (Throwable $e) {
            throw new DatabaseException('Gagal memperbarui akun guru: ' . $e->getMessage(), null, [], [], 0, $e);
        }
    }
}
