<?php

namespace App\Repositories;

use App\Contracts\Interfaces\ShiftRepositoryInterface;
use App\Models\Shift;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ShiftRepository implements ShiftRepositoryInterface
{
    /**
     * Shift model instance
     */
    protected Shift $shiftModel;

    /**
     * ShiftRepository constructor
     */
    public function __construct(Shift $shiftModel)
    {
        $this->shiftModel = $shiftModel;
    }

    /**
     * Get all shifts
     */
    public function getAllShifts(array $filters = []): Collection
    {
        $query = $this->shiftModel->newQuery();

        // Apply filters
        if (! empty($filters)) {
            $query = $this->applyFilters($query, $filters);
        }

        return $query->get();
    }

    /**
     * Get a shift by ID
     */
    public function getShiftById(int $id): Shift
    {
        return $this->shiftModel->findOrFail($id);
    }

    /**
     * Create a new shift
     */
    public function createShift(array $data): Shift
    {
        DB::beginTransaction();
        try {
            $shift = $this->shiftModel->create([
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
                'status' => $data['status'] ?? 'active',
            ]);

            DB::commit();

            return $shift;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Create shift failed: '.$e->getMessage());
            throw new \Exception('Gagal menyimpan shift: '.$e->getMessage());
        }
    }

    /**
     * Update a shift
     */
    public function updateShift(int $id, array $data): bool
    {
        DB::beginTransaction();
        try {
            $shift = $this->getShiftById($id);

            $updateData = [
                'name' => $data['name'] ?? $shift->name,
                'description' => $data['description'] ?? $shift->description,
                'status' => $data['status'] ?? $shift->status,
            ];

            // Check if data has actually changed
            if (
                $shift->name === $updateData['name'] &&
                $shift->description === $updateData['description'] &&
                $shift->status === $updateData['status']
            ) {
                DB::rollBack();

                return false;
            }

            $shift->fill($updateData);
            $shift->save();

            DB::commit();

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Update shift failed: '.$e->getMessage());
            throw new \Exception('Gagal memperbarui shift: '.$e->getMessage());
        }
    }

    /**
     * Delete a shift
     */
    public function deleteShift(int $id): bool
    {
        DB::beginTransaction();
        try {
            $shift = $this->getShiftById($id);

            // Check if shift is being used by classrooms or lesson hours
            if ($shift->classrooms()->count() > 0 || $shift->lessonHours()->count() > 0) {
                throw new \Exception('Shift tidak dapat dihapus karena sedang digunakan oleh kelas atau jam pelajaran.');
            }

            $shift->delete();
            DB::commit();

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Delete shift failed: '.$e->getMessage());
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * Get all active shifts
     */
    public function getAllActiveShifts(): Collection
    {
        return $this->shiftModel->where('status', 'active')->get();
    }

    /**
     * Apply filters to the query
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    private function applyFilters($query, array $filters)
    {
        // Filter by status
        if (! empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // Search by name
        if (! empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            });
        }

        return $query;
    }

    /**
     * Count total number of shifts
     */
    public function count(): int
    {
        return $this->shiftModel->count();
    }
}
