<?php

namespace App\Http\Controllers\Admin;

use App\Services\TeacherService;
use App\Enums\LeaveStatusEnum;
use App\Enums\LeaveTypeEnum;
use App\Enums\RoleEnum;
use App\Exceptions\BusinessLogicException;
use App\Exceptions\DatabaseException;
use App\Exceptions\NotFoundException;
use App\Http\Controllers\Controller;
use App\Http\Requests\LeaveRequests\StoreLeaveRequest;
use App\Http\Requests\LeaveRequests\UpdateLeaveRequest;
use App\Models\User;
use App\Services\LeaveRequestService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class LeaveRequestController extends Controller
{
    /**
     * LeaveRequestController constructor
     */
    public function __construct(
        protected LeaveRequestService $leaveRequestService,
        protected TeacherService $teacherService
    ) {
    }

    /**
     * Display a listing of all leave requests (for admin)
     */
    public function index(): View|JsonResponse
    {
        try {
            $leaveRequests = $this->leaveRequestService->getAllLeaveRequests();

            if (request()->ajax()) {
                return $this->datatableResponse($leaveRequests);
            }

            return view('admin.pages.leave-requests.index', [
                'leaveRequests' => $leaveRequests,
            ]);
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return $this->errorResponse('Terjadi kesalahan saat memuat data cuti', Response::HTTP_INTERNAL_SERVER_ERROR);
            }
            abort(Response::HTTP_INTERNAL_SERVER_ERROR, 'Terjadi kesalahan saat memuat data cuti');
        }
    }

    /**
     * Format response for DataTables
     *
     * @param  mixed  $data
     */
    private function datatableResponse($data): JsonResponse
    {
        return datatables()
            ->of($data)
            ->addIndexColumn()
            ->editColumn('employee_name', fn($row) => $row->teacher->user->name ?? '-')
            ->editColumn('leave_type', function ($row) {
                return LeaveTypeEnum::getLabel($row->leave_type);
            })
            ->editColumn('leave_date', fn($row) => $row->leave_date ? date('d/m/Y', strtotime($row->leave_date)) : '-')
            ->editColumn('status', function ($row) {
                $status = $row->status instanceof LeaveStatusEnum ? $row->status : LeaveStatusEnum::from($row->status);
                $color = $status->color();
                $label = LeaveStatusEnum::getLabel($status);

                return '<span class="badge bg-' . $color . ' text-uppercase">' . $label . '</span>';
            })
            ->addColumn(
                'action',
                fn($row) => view('admin.components.button-actions-v2', [
                    'id' => $row->id,
                    'edit' => route('admin.leave-requests.edit', $row->id),
                    'destroy' => route('admin.leave-requests.destroy', $row->id),
                ])
            )
            ->rawColumns(['status', 'action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new leave request
     */
    public function create(): View
    {
        // Get active teachers
        $teachers = $this->teacherService->getAllTeachers(['status' => 'active']);

        return view('admin.pages.leave-requests.create', [
            'teachers' => $teachers,
        ]);
    }

    /**
     * Store a newly created leave request
     */
    public function store(StoreLeaveRequest $request): JsonResponse
    {
        try {
            $leaveRequest = $this->leaveRequestService->createLeaveRequest($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Pengajuan cuti berhasil dibuat',
                'data' => $leaveRequest,
            ]);
        } catch (BusinessLogicException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_BAD_REQUEST);
        } catch (DatabaseException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            return $this->errorResponse('Terjadi kesalahan saat membuat pengajuan cuti: ' . $e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Show the form for editing the specified leave request
     */
    public function edit(int $id): View
    {
        try {
            $leaveRequest = $this->leaveRequestService->getLeaveRequestById($id);

            // Get active teachers
            $teachers = $this->teacherService->getAllTeachers(['status' => 'active']);

            // Get users who can approve leave requests (admin, principal)
            $approvers = User::role([RoleEnum::ADMIN->value, RoleEnum::PRINCIPAL->value])
                ->where('status', 'active')
                ->get();

            return view('admin.pages.leave-requests.edit', [
                'leaveRequest' => $leaveRequest,
                'teachers' => $teachers,
                'approvers' => $approvers,
            ]);
        } catch (NotFoundException $e) {
            abort(Response::HTTP_NOT_FOUND, $e->getMessage());
        }
    }

    /**
     * Update the specified leave request
     */
    public function update(UpdateLeaveRequest $request, int $id): JsonResponse
    {
        try {
            $this->leaveRequestService->updateLeaveRequest($id, $request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Pengajuan cuti berhasil diperbarui',
            ]);
        } catch (BusinessLogicException $e) {
            // For business logic exceptions like 'no changes made'
            return response()->json([
                'success' => true,
                'message' => $e->getMessage(),
            ]);
        } catch (NotFoundException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (DatabaseException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            return $this->errorResponse('Terjadi kesalahan saat memperbarui pengajuan cuti: ' . $e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified leave request
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->leaveRequestService->deleteLeaveRequest($id);

            return response()->json([
                'success' => true,
                'message' => 'Pengajuan cuti berhasil dihapus',
            ]);
        } catch (BusinessLogicException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_BAD_REQUEST);
        } catch (NotFoundException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (DatabaseException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            return $this->errorResponse('Terjadi kesalahan saat menghapus pengajuan cuti: ' . $e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Error response helper
     */
    protected function errorResponse(string $message, int $statusCode = Response::HTTP_BAD_REQUEST, ?array $errors = null, array $headers = []): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $message,
        ];

        if ($errors) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $statusCode, $headers);
    }
}
