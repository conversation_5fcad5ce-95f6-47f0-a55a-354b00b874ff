<?php

namespace App\Providers;

use App\Contracts\Interfaces\AcademicYearRepositoryInterface;
use App\Contracts\Interfaces\AttendanceRepositoryInterface;
use App\Contracts\Interfaces\ClassroomRepositoryInterface;
use App\Contracts\Interfaces\ClassScheduleRepositoryInterface;
use App\Contracts\Interfaces\LeaveRequestRepositoryInterface;
use App\Contracts\Interfaces\LessonHourRepositoryInterface;
use App\Contracts\Interfaces\ProgramRepositoryInterface;
use App\Contracts\Interfaces\ShiftRepositoryInterface;
use App\Contracts\Interfaces\StudentRepositoryInterface;
use App\Contracts\Interfaces\SubjectRepositoryInterface;
use App\Contracts\Interfaces\TeacherAssignmentRepositoryInterface;
use App\Contracts\Interfaces\TeacherRepositoryInterface;
use App\Contracts\Interfaces\UserRepositoryInterface;
use App\Repositories\AcademicYearRepository;
use App\Repositories\AttendanceRepository;
use App\Repositories\ClassroomRepository;
use App\Repositories\ClassScheduleRepository;
use App\Repositories\LeaveRequestRepository;
use App\Repositories\LessonHourRepository;
use App\Repositories\ProgramRepository;
use App\Repositories\ShiftRepository;
use App\Repositories\StudentRepository;
use App\Repositories\SubjectRepository;
use App\Repositories\TeacherAssignmentRepository;
use App\Repositories\TeacherRepository;
use App\Repositories\UserRepository;
use App\Services\AcademicYearService;
use App\Services\AttendanceService;
use App\Services\ClassroomService;
use App\Services\ClassScheduleService;
use App\Services\LeaveRequestService;
use App\Services\LessonHourService;
use App\Services\ProgramService;
use App\Services\ShiftService;
use App\Services\StaffService;
use App\Services\StudentService;
use App\Services\StudentEnrollmentService;
use App\Services\StudentRegistrationService;
use App\Services\SubjectService;
use App\Services\TeacherAssignmentService;
use App\Services\TeacherService;
use App\Services\UserService;
use App\Models\User;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register UserRepository
        $this->app->bind(
            UserRepositoryInterface::class,
            UserRepository::class
        );

        // Register UserService with automatic dependency injection
        $this->app->bind(UserService::class, function ($app) {
            return new UserService(
                $app->make(UserRepositoryInterface::class)
            );
        });

        // Register StaffService with automatic dependency injection
        $this->app->bind(StaffService::class, function ($app) {
            return new StaffService(
                $app->make(UserRepositoryInterface::class)
            );
        });

        // Register TeacherRepository
        $this->app->bind(
            TeacherRepositoryInterface::class,
            TeacherRepository::class
        );

        // Register TeacherService with automatic dependency injection
        $this->app->bind(TeacherService::class, function ($app) {
            return new TeacherService(
                $app->make(TeacherRepositoryInterface::class),
                $app->make(UserRepositoryInterface::class)
            );
        });

        // Register StudentRepository
        $this->app->bind(
            StudentRepositoryInterface::class,
            StudentRepository::class
        );

        // Register StudentEnrollmentService
        $this->app->bind(StudentEnrollmentService::class, function ($app) {
            return new StudentEnrollmentService(
                $app->make(StudentRepositoryInterface::class)
            );
        });

        // Register StudentRegistrationService
        $this->app->bind(StudentRegistrationService::class, function ($app) {
            return new StudentRegistrationService(
                $app->make(StudentRepositoryInterface::class),
                $app->make(UserRepositoryInterface::class)
            );
        });

        // Register StudentService with automatic dependency injection
        $this->app->bind(StudentService::class, function ($app) {
            return new StudentService(
                $app->make(StudentRepositoryInterface::class),
                $app->make(UserRepositoryInterface::class),
                $app->make(StudentRegistrationService::class),
                $app->make(StudentEnrollmentService::class)
            );
        });

        // Register ProgramRepository
        $this->app->bind(
            ProgramRepositoryInterface::class,
            ProgramRepository::class
        );

        // Register ProgramService with automatic dependency injection
        $this->app->bind(ProgramService::class, function ($app) {
            return new ProgramService(
                $app->make(ProgramRepositoryInterface::class)
            );
        });

        // Register AcademicYearRepository
        $this->app->bind(
            AcademicYearRepositoryInterface::class,
            AcademicYearRepository::class
        );

        // Register AcademicYearService with automatic dependency injection
        $this->app->bind(AcademicYearService::class, function ($app) {
            return new AcademicYearService(
                $app->make(AcademicYearRepositoryInterface::class)
            );
        });

        // Register ClassroomRepository
        $this->app->bind(
            ClassroomRepositoryInterface::class,
            ClassroomRepository::class
        );

        // Register ClassroomService with automatic dependency injection
        $this->app->bind(ClassroomService::class, function ($app) {
            return new ClassroomService(
                $app->make(ClassroomRepositoryInterface::class)
            );
        });

        // Register SubjectRepository
        $this->app->bind(
            SubjectRepositoryInterface::class,
            SubjectRepository::class
        );

        // Register SubjectService with automatic dependency injection
        $this->app->bind(SubjectService::class, function ($app) {
            return new SubjectService(
                $app->make(SubjectRepositoryInterface::class)
            );
        });

        // Register LessonHourRepository
        $this->app->bind(
            LessonHourRepositoryInterface::class,
            LessonHourRepository::class
        );

        // Register LessonHourService with automatic dependency injection
        $this->app->bind(LessonHourService::class, function ($app) {
            return new LessonHourService(
                $app->make(LessonHourRepositoryInterface::class)
            );
        });

        // Register TeacherAssignmentRepository
        $this->app->bind(
            TeacherAssignmentRepositoryInterface::class,
            TeacherAssignmentRepository::class
        );

        // Register TeacherAssignmentService with automatic dependency injection
        $this->app->bind(TeacherAssignmentService::class, function ($app) {
            return new TeacherAssignmentService(
                $app->make(TeacherAssignmentRepositoryInterface::class),
                $app->make(TeacherRepositoryInterface::class),
                $app->make(SubjectRepositoryInterface::class),
                $app->make(ClassroomRepositoryInterface::class)
            );
        });

        // Register ClassScheduleRepository
        $this->app->bind(
            ClassScheduleRepositoryInterface::class,
            ClassScheduleRepository::class
        );

        // Register ClassScheduleService with automatic dependency injection
        $this->app->bind(ClassScheduleService::class, function ($app) {
            return new ClassScheduleService(
                $app->make(ClassScheduleRepositoryInterface::class)
            );
        });

        // Register ShiftRepository
        $this->app->bind(
            ShiftRepositoryInterface::class,
            ShiftRepository::class
        );

        // Register ShiftService with automatic dependency injection
        $this->app->bind(ShiftService::class, function ($app) {
            return new ShiftService(
                $app->make(ShiftRepositoryInterface::class)
            );
        });

        // Register AttendanceRepository
        $this->app->bind(
            AttendanceRepositoryInterface::class,
            AttendanceRepository::class
        );

        // Register AttendanceService with automatic dependency injection
        $this->app->bind(AttendanceService::class, function ($app) {
            return new AttendanceService(
                $app->make(AttendanceRepositoryInterface::class)
            );
        });

        // Register LeaveRequestRepository
        $this->app->bind(
            LeaveRequestRepositoryInterface::class,
            LeaveRequestRepository::class
        );

        // Register LeaveRequestService with automatic dependency injection
        $this->app->bind(LeaveRequestService::class, function ($app) {
            return new LeaveRequestService(
                $app->make(LeaveRequestRepositoryInterface::class)
            );
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
