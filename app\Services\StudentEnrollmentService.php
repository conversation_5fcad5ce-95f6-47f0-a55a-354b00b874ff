<?php

namespace App\Services;

use App\Contracts\Interfaces\StudentRepositoryInterface;
use App\Exceptions\BusinessLogicException;
use App\Exceptions\DatabaseException;
use App\Exceptions\NotFoundException;
use App\Models\AcademicYear;
use App\Models\Classroom;
use App\Models\Student;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;

class StudentEnrollmentService
{
    /**
     * The student repository instance.
     */
    protected StudentRepositoryInterface $studentRepository;

    /**
     * Create a new StudentEnrollmentService instance.
     */
    public function __construct(StudentRepositoryInterface $studentRepository)
    {
        $this->studentRepository = $studentRepository;
    }

    /**
     * Enroll a student to a classroom for a specific academic year.
     *
     * @param int $studentId Student ID
     * @param int $classroomId Classroom ID
     * @param int $academicYearId Academic Year ID
     * @throws \App\Exceptions\BusinessLogicException If the student is already enrolled in this classroom for this academic year
     * @throws \App\Exceptions\NotFoundException If the student, classroom, or academic year is not found
     * @throws \App\Exceptions\DatabaseException If there is a database error
     * @return bool
     */
    public function enrollStudentToClassroom(int $studentId, int $classroomId, int $academicYearId): bool
    {
        try {
            // Start a transaction
            return DB::transaction(function () use ($studentId, $classroomId, $academicYearId) {
                // Find the student
                $student = $this->studentRepository->findById($studentId);

                // Check if student is already enrolled in this classroom for this academic year
                $exists = $student->classrooms()
                    ->wherePivot('classroom_id', $classroomId)
                    ->wherePivot('academic_year_id', $academicYearId)
                    ->exists();

                if ($exists) {
                    throw new BusinessLogicException('Siswa sudah terdaftar di kelas ini untuk tahun akademik ini');
                }

                // Check if classroom exists
                $classroom = Classroom::find($classroomId);
                if (!$classroom) {
                    throw new NotFoundException("Kelas dengan ID {$classroomId} tidak ditemukan");
                }

                // Check if academic year exists
                $academicYear = AcademicYear::find($academicYearId);
                if (!$academicYear) {
                    throw new NotFoundException("Tahun akademik dengan ID {$academicYearId} tidak ditemukan");
                }

                // Attach the student to the classroom
                $student->classrooms()->attach($classroomId, [
                    'academic_year_id' => $academicYearId,
                ]);

                return true;
            });
        } catch (BusinessLogicException | NotFoundException $e) {
            // Just rethrow these exceptions as they are already properly formatted
            throw $e;
        } catch (Throwable $e) {
            // Convert any other exceptions to DatabaseException
            throw new DatabaseException('Gagal mendaftarkan siswa ke kelas: ' . $e->getMessage(), null, [], [], 0, $e);
        }
    }

    /**
     * Get all classrooms where a student is enrolled.
     *
     * @param int $studentId Student ID
     * @throws \App\Exceptions\NotFoundException If the student is not found
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getStudentClassrooms(int $studentId): Collection
    {
        try {
            // Find the student
            $student = $this->studentRepository->findById($studentId);

            // Get the classrooms
            return $student->classrooms()->with('academicYear')->get();
        } catch (NotFoundException $e) {
            // Just rethrow these exceptions as they are already properly formatted
            throw $e;
        } catch (Throwable $e) {
            // Convert any other exceptions to DatabaseException
            throw new DatabaseException('Gagal mendapatkan kelas siswa: ' . $e->getMessage(), null, [], [], 0, $e);
        }
    }

    /**
     * Get all students enrolled in a specific classroom for a specific academic year.
     *
     * @param int $classroomId Classroom ID
     * @param int $academicYearId Academic Year ID
     * @throws \App\Exceptions\NotFoundException If the classroom or academic year is not found
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getClassroomStudents(int $classroomId, int $academicYearId): Collection
    {
        try {
            // Check if classroom exists
            $classroom = Classroom::find($classroomId);
            if (!$classroom) {
                throw new NotFoundException("Kelas dengan ID {$classroomId} tidak ditemukan");
            }

            // Check if academic year exists
            $academicYear = AcademicYear::find($academicYearId);
            if (!$academicYear) {
                throw new NotFoundException("Tahun akademik dengan ID {$academicYearId} tidak ditemukan");
            }

            // Get the students
            return Student::whereHas('classrooms', function ($query) use ($classroomId, $academicYearId) {
                $query->where('classroom_id', $classroomId)
                    ->where('academic_year_id', $academicYearId);
            })->with('user')->get();
        } catch (NotFoundException $e) {
            // Just rethrow these exceptions as they are already properly formatted
            throw $e;
        } catch (Throwable $e) {
            // Convert any other exceptions to DatabaseException
            throw new DatabaseException('Gagal mendapatkan siswa kelas: ' . $e->getMessage(), null, [], [], 0, $e);
        }
    }
}
