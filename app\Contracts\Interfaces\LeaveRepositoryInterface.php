<?php

namespace App\Contracts\Interfaces;

interface LeaveRepositoryInterface
{
    /**
     * Request a new leave
     *
     * @param  array  $data  Leave request data
     * @return mixed The created leave request
     */
    public function requestLeave(array $data);

    /**
     * Approve a leave request
     *
     * @param  int  $leaveId  The ID of the leave request
     * @param  int  $approverId  The ID of the approver
     * @return mixed The updated leave request
     */
    public function approveLeave(int $leaveId, int $approverId);

    /**
     * Reject a leave request
     *
     * @param  int  $leaveId  The ID of the leave request
     * @param  int  $approverId  The ID of the approver
     * @param  string  $reason  The reason for rejection
     * @return mixed The updated leave request
     */
    public function rejectLeave(int $leaveId, int $approverId, string $reason);

    /**
     * Get leaves for a specific teacher
     *
     * @param  int  $teacherId  The ID of the teacher
     * @param  array  $filters  Optional filters
     * @return mixed Collection of leave requests
     */
    public function getTeacherLeaves(int $teacherId, array $filters);

    /**
     * Get all pending leave requests
     *
     * @return mixed Collection of pending leave requests
     */
    public function getPendingLeaves();

    /**
     * Add a task to a leave request
     *
     * @param  int  $leaveId  The ID of the leave request
     * @param  array  $data  Task data
     * @return mixed The created task
     */
    public function addLeaveTask(int $leaveId, array $data);
}
