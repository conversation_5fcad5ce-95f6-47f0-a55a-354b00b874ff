<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'username' => $this->faker->unique()->userName(),
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'photo_path' => $this->faker->optional()->imageUrl(200, 200, 'people'),
            'password' => bcrypt('password'),
            // 'email_verified_at' => $this->faker->optional(0.7)->dateTimeThisYear(), // 70% sudah verifikasi
            'phone_number' => $this->faker->optional()->phoneNumber(),
            'status' => $this->faker->boolean(90), // 90% aktif, 10% non-aktif
            'last_login_at' => $this->faker->optional(0.8)->dateTimeThisMonth(), // 80% sudah login
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }
}
