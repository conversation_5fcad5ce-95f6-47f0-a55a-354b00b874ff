<?php

namespace App\Contracts\Interfaces;

use App\Exceptions\BusinessLogicException;
use App\Exceptions\DatabaseException;
use App\Exceptions\NotFoundException;
use App\Models\LeaveRequest;
use Illuminate\Database\Eloquent\Collection;

interface LeaveRequestRepositoryInterface
{
    /**
     * Get all leave requests with optional filtering.
     */
    public function getAll(array $filters = []): Collection;

    /**
     * Create a new leave request.
     *
     * @throws BusinessLogicException
     * @throws DatabaseException
     */
    public function create(array $data): LeaveRequest;

    /**
     * Update an existing leave request.
     *
     * @throws BusinessLogicException
     * @throws DatabaseException
     * @throws NotFoundException
     */
    public function update(int $id, array $data): bool;

    /**
     * Delete a leave request.
     *
     * @throws BusinessLogicException
     * @throws DatabaseException
     * @throws NotFoundException
     */
    public function delete(int $id): bool;

    /**
     * Find a leave request by ID.
     *
     * @throws NotFoundException
     */
    public function findById(int $id): LeaveRequest;

    /**
     * Find overlapping leave requests for a teacher.
     */
    public function findOverlappingLeaves(int $teacherId, string $leaveDate, ?int $excludeId = null): Collection;

    /**
     * Get leave requests by teacher ID.
     */
    public function getByEmployeeId(int $teacherId, array $filters = []): Collection;

    /**
     * Get leave requests by status.
     */
    public function getByStatus(string $status, array $filters = []): Collection;

    /**
     * Count total leave requests.
     */
    public function count(): int;
}
