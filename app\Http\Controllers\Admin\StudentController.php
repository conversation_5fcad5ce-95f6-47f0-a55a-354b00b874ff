<?php

namespace App\Http\Controllers\Admin;

use App\Contracts\Interfaces\StudentRepositoryInterface;
use App\Services\StudentEnrollmentService;
use App\Services\StudentRegistrationService;
use App\Services\StudentService;
use App\Enums\GenderEnum;
use App\Enums\ReligionEnum;
use App\Enums\UserStatus;
use App\Exceptions\BusinessLogicException;
use App\Exceptions\DatabaseException;
use App\Exceptions\NotFoundException;
use App\Exports\StudentExport;
use App\Exports\StudentTemplateExport;
use App\Helpers\ErrorHandler;
use App\Http\Controllers\Controller;
use App\Http\Requests\StudentRequests\StudentStoreRequest;
use App\Imports\StudentImport;
use App\Models\AcademicYear;
use App\Models\Classroom;
use App\Models\Student;
use App\Models\User;
use App\Services\UserService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Throwable;
use Yajra\DataTables\Facades\DataTables;

class StudentController extends Controller
{
    /**
     * Student service instance
     */
    protected StudentService $studentService;

    /**
     * Student repository instance for data retrieval
     */
    protected StudentRepositoryInterface $studentRepository;

    /**
     * Student registration service instance
     */
    protected StudentRegistrationService $studentRegistrationService;

    /**
     * Student enrollment service instance
     */
    protected StudentEnrollmentService $studentEnrollmentService;

    /**
     * User service instance
     */
    protected UserService $userService;

    /**
     * StudentController constructor
     */
    public function __construct(
        StudentService $studentService,
        StudentRepositoryInterface $studentRepository,
        StudentRegistrationService $studentRegistrationService,
        StudentEnrollmentService $studentEnrollmentService,
        UserService $userService
    ) {
        $this->studentService = $studentService;
        $this->studentRepository = $studentRepository;
        $this->studentRegistrationService = $studentRegistrationService;
        $this->studentEnrollmentService = $studentEnrollmentService;
        $this->userService = $userService;
    }

    /**
     * Display a listing of students
     *
     * @return View|JsonResponse
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $students = $this->studentRepository->getAllWithRelations(['user', 'classrooms'], $request->all());

            return $this->datatableResponse($students);
        }

        $classrooms = Classroom::all();

        return view('admin.pages.student.index', [
            'statuses' => UserStatus::dropdown(),
            'genders' => GenderEnum::options(),
            'classrooms' => $classrooms,
        ]);
    }

    /**
     * Format response for DataTables
     *
     * @param  mixed  $data
     */
    public function datatableResponse($data): JsonResponse
    {
        return datatables()
            ->of($data)
            ->addIndexColumn()
            ->addColumn('gender', function ($row) {
                return GenderEnum::getLabel($row->gender);
            })
            ->addColumn('birth_date', function ($row) {
                return date('d/m/Y', strtotime($row->birth_date));
            })
            ->addColumn('classrooms', function ($row) {
                $classrooms = $row->classrooms()->with('academicYear')
                    ->withActiveAcademicYear()
                    ->get();

                $html = '';
                if ($classrooms->count() > 0) {
                    foreach ($classrooms as $classroom) {
                        $html .= '<span class="badge bg-primary-subtle text-primary">' . $classroom->name . '</span> ';
                    }
                } else {
                    $html = '<span class="badge bg-warning-subtle text-warning">Belum ada kelas</span>';
                }

                return $html;
            })
            ->addColumn('status', function ($row) {
                return '<span class="badge bg-' . $row->user->status->color() . ' text-uppercase">' . $row->user->status->label() . '</span>';
            })
            ->addColumn('action', function ($row) {
                return view('admin.components.student-actions', [
                    'id' => $row->id,
                    'editUrl' => route('admin.students.edit', $row->id),
                    'viewUrl' => route('admin.students.show', $row->id),
                    'enrollUrl' => route('admin.students.enroll', $row->id),
                    'deleteUrl' => route('admin.students.destroy', $row->id),
                ]);
            })
            ->rawColumns(['action', 'classrooms', 'status'])
            ->make(true);
    }

    /**
     * Show the form for creating a new student
     */
    public function create(): View
    {
        return view('admin.pages.student.create', [
            'statuses' => UserStatus::dropdown(),
            'genders' => GenderEnum::options(),
            'religions' => ReligionEnum::options(),
        ]);
    }

    /**
     * Store a newly created student
     */
    public function store(StudentStoreRequest $request): JsonResponse
    {
        try {
            // Handle profile picture upload
            $profilePicturePath = null;
            if ($request->hasFile('profile_picture')) {
                $profilePicturePath = $request->file('profile_picture')->store('students/profile-pictures', 'public');
            }

            // Create student with user account via StudentRegistrationService
            $data = $request->validated();
            $data['profile_picture'] = $profilePicturePath;

            $this->studentRegistrationService->registerStudent($data);

            return response()->json([
                'success' => true,
                'message' => 'Data siswa berhasil ditambahkan',
            ]);
        } catch (BusinessLogicException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (DatabaseException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified student.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function show(Student $student)
    {
        $student->load([
            'user',
            'classrooms' => function ($query) {
                $query->with('academicYear');
            },
        ]);

        $academicYears = AcademicYear::where('status', 'active')->get();
        $classrooms = $student->classrooms;

        return view('admin.pages.student.show', [
            'student' => $student,
            'classrooms' => $classrooms,
            'academicYears' => $academicYears,
        ]);
    }

    /**
     * Show the form for editing the specified student
     */
    public function edit(int $id): View
    {
        $student = $this->studentService->getStudentById($id);
        $student->load('user');

        return view('admin.pages.student.edit', [
            'student' => $student,
            'statuses' => UserStatus::dropdown(),
            'genders' => GenderEnum::options(),
            'religions' => ReligionEnum::options(),
        ]);
    }

    /**
     * Update the specified student
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            // Handle profile picture upload
            $data = $request->all();

            if ($request->hasFile('profile_picture')) {
                $data['profile_picture'] = $request->file('profile_picture')->store('students/profile-pictures', 'public');

                // Delete old profile picture if exists
                $student = $this->studentService->getStudentById($id);
                if ($student->profile_picture) {
                    Storage::disk('public')->delete($student->profile_picture);
                }
            }

            // Update student via StudentRegistrationService
            $this->studentRegistrationService->updateStudent($id, $data);

            return response()->json([
                'success' => true,
                'message' => 'Data siswa berhasil diperbarui',
            ]);
        } catch (NotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 404);
        } catch (BusinessLogicException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (DatabaseException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified student
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            // Delete student via StudentRegistrationService
            $this->studentRegistrationService->deleteStudent($id);

            return response()->json([
                'success' => true,
                'message' => 'Data siswa berhasil dihapus',
            ]);
        } catch (NotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 404);
        } catch (BusinessLogicException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (DatabaseException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Enroll student to a classroom
     */
    public function enroll(Request $request, int $id): JsonResponse
    {
        try {
            $request->validate([
                'classroom_id' => 'required|exists:classrooms,id',
                'academic_year_id' => 'required|exists:academic_years,id',
            ]);

            // Check if student is already enrolled in this classroom for this academic year
            $student = $this->studentService->getStudentById($id);

            // Enroll student via StudentEnrollmentService
            $this->studentEnrollmentService->enrollStudentToClassroom(
                $id,
                $request->classroom_id,
                $request->academic_year_id
            );

            return response()->json([
                'success' => true,
                'message' => 'Siswa berhasil didaftarkan ke kelas',
            ]);
        } catch (NotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 404);
        } catch (BusinessLogicException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (DatabaseException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Import students data from Excel
     */
    public function import(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'import_file' => 'required|file|mimes:xlsx,xls,csv|max:10240',
            ]);

            DB::beginTransaction();

            try {
                Excel::import(new StudentImport, $request->file('import_file'));
                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'Data siswa berhasil diimpor',
                ]);
            } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
                DB::rollBack();

                $failures = $e->failures();
                $errors = [];

                foreach ($failures as $failure) {
                    $errors[] = 'Baris ' . $failure->row() . ': ' . implode(', ', $failure->errors());
                }

                return response()->json([
                    'success' => false,
                    'message' => 'Validasi data gagal',
                    'errors' => $errors,
                ], 422);
            }
        } catch (BusinessLogicException $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (Throwable $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Export students data to Excel
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(Request $request)
    {
        try {
            $filters = $request->all();

            return Excel::download(
                new StudentExport($filters),
                'daftar-siswa-' . date('Y-m-d-His') . '.xlsx'
            );
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Download import template
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function template()
    {
        try {
            return Excel::download(
                new StudentTemplateExport(),
                'template-impor-siswa.xlsx'
            );
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Enroll multiple students to a classroom.
     */
    public function enrollMultiple(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'classroom_id' => 'required|exists:classrooms,id',
                'student_ids' => 'required|array',
                'student_ids.*' => 'exists:students,id',
            ]);

            DB::beginTransaction();

            $classroom = Classroom::findOrFail($request->classroom_id);
            $studentIds = $request->student_ids;

            // Check capacity
            $currentCount = $classroom->currentStudents()->count();
            $newStudentsCount = count($studentIds);

            if ($currentCount + $newStudentsCount > $classroom->capacity) {
                ErrorHandler::businessLogic('Kapasitas kelas tidak mencukupi');
            }

            // Enroll each student
            foreach ($studentIds as $studentId) {
                try {
                    $this->studentEnrollmentService->enrollStudentToClassroom(
                        $studentId,
                        $classroom->id,
                        $classroom->academic_year_id
                    );
                } catch (BusinessLogicException $e) {
                    // If student is already enrolled, continue to next student
                    continue;
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Siswa berhasil ditambahkan ke kelas',
            ]);
        } catch (BusinessLogicException $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (NotFoundException $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 404);
        } catch (DatabaseException $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        } catch (Throwable $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get available students for a classroom.
     */
    public function getAvailableStudents(Request $request, Classroom $classroom): JsonResponse
    {
        try {
            $search = $request->search ?? '';
            $page = $request->page ?? 1;
            $perPage = 10;

            $query = Student::whereDoesntHave('classrooms', function ($query) use ($classroom) {
                $query->where('classroom_students.academic_year_id', $classroom->academic_year_id);
            })->whereHas('user', function ($query) {
                $query->where('status', 'active');
            });

            // Apply search filter
            if (!empty($search)) {
                $query->where(function ($q) use ($search) {
                    $q->whereHas('user', function ($userQuery) use ($search) {
                        $userQuery->where('name', 'like', "%{$search}%")
                            ->orWhere('email', 'like', "%{$search}%");
                    })
                        ->orWhere('nis', 'like', "%{$search}%")
                        ->orWhere('nisn', 'like', "%{$search}%");
                });
            }

            $totalRecords = $query->count();
            $lastPage = ceil($totalRecords / $perPage);

            $students = $query->with('user')
                ->skip(($page - 1) * $perPage)
                ->take($perPage)
                ->get()
                ->map(function ($student) {
                    return [
                        'id' => $student->id,
                        'name' => $student->user->name,
                        'nis' => $student->nis,
                        'nisn' => $student->nisn,
                        'email' => $student->user->email,
                    ];
                });

            return response()->json([
                'data' => $students,
                'current_page' => (int) $page,
                'last_page' => $lastPage,
                'total' => $totalRecords,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove a student from a classroom.
     */
    public function removeFromClassroom(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'student_id' => 'required|exists:students,id',
                'classroom_id' => 'required|exists:classrooms,id',
            ]);

            DB::beginTransaction();

            $classroom = Classroom::findOrFail($request->classroom_id);
            $student = Student::findOrFail($request->student_id);

            // Detach student from classroom
            $deleted = DB::table('classroom_students')
                ->where('classroom_id', $classroom->id)
                ->where('student_id', $student->id)
                ->where('academic_year_id', $classroom->academic_year_id)
                ->delete();

            if (!$deleted) {
                ErrorHandler::notFound('Siswa tidak terdaftar di kelas ini');
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Siswa berhasil dikeluarkan dari kelas',
            ]);
        } catch (BusinessLogicException $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (NotFoundException $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 404);
        } catch (DatabaseException $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        } catch (Throwable $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Change the status of a student's account
     */
    public function changeStatus(Request $request, int $id): JsonResponse
    {
        try {
            $request->validate([
                'status' => 'required|boolean',
            ]);

            $student = $this->studentService->getStudentById($id);
            $status = $request->status ? UserStatus::Active : UserStatus::Inactive;

            // Prevent changing own status
            if ($student->user->id === auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tidak dapat mengubah status akun sendiri.',
                ], 400);
            }

            $student->user->status = $status;
            $student->user->save();

            $statusLabel = $status === UserStatus::Active ? 'aktif' : 'nonaktif';

            return response()->json([
                'success' => true,
                'message' => "Status siswa berhasil diubah menjadi {$statusLabel}",
            ]);
        } catch (NotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengubah status siswa: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update student account settings
     */
    public function updateAccount(Request $request, int $id): JsonResponse
    {
        try {
            $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|email|max:255|unique:users,email,' . $id . ',id',
                'password' => 'nullable|string|min:8',
            ]);

            $student = $this->studentService->getStudentById($id);
            $user = $student->user;

            $user->name = $request->name;
            $user->email = $request->email;

            if (!empty($request->password)) {
                $user->password = Hash::make($request->password);
            }

            $user->save();

            return response()->json([
                'success' => true,
                'message' => 'Akun siswa berhasil diperbarui',
            ]);
        } catch (NotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui akun siswa: ' . $e->getMessage(),
            ], 500);
        }
    }
}
