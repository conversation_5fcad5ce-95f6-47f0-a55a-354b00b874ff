<?php

namespace App\Http\Requests\StudentRequests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StudentUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $studentId = $this->route('student');

        return [
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'email',
                Rule::unique('users', 'email')->ignore($this->user_id),
            ],
            'password' => 'nullable|min:6',
            'status' => 'required|string|in:active,inactive',
            'nis' => [
                'required',
                'string',
                'max:20',
                Rule::unique('students', 'nis')->ignore($studentId),
            ],
            'nisn' => [
                'required',
                'string',
                'max:20',
                Rule::unique('students', 'nisn')->ignore($studentId),
            ],
            'birth_place' => 'required|string|max:100',
            'birth_date' => 'required|date',
            'gender' => 'required|in:male,female',
            'religion' => 'required|string|max:20',
            'address' => 'required|string',
            'phone' => 'nullable|string|max:15',
            'parent_name' => 'required|string|max:255',
            'parent_phone' => 'required|string|max:15',
            'parent_occupation' => 'nullable|string|max:100',
            'parent_address' => 'nullable|string',
            'entry_year' => 'required|digits:4|integer|min:2000|max:'.(date('Y') + 1),
            'profile_picture' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'name' => 'Nama lengkap',
            'email' => 'Email',
            'password' => 'Password',
            'status' => 'Status',
            'nis' => 'NIS',
            'nisn' => 'NISN',
            'birth_place' => 'Tempat lahir',
            'birth_date' => 'Tanggal lahir',
            'gender' => 'Jenis kelamin',
            'religion' => 'Agama',
            'address' => 'Alamat',
            'phone' => 'Nomor telepon',
            'parent_name' => 'Nama orang tua/wali',
            'parent_phone' => 'Nomor telepon orang tua/wali',
            'parent_occupation' => 'Pekerjaan orang tua/wali',
            'parent_address' => 'Alamat orang tua/wali',
            'entry_year' => 'Tahun masuk',
            'profile_picture' => 'Foto profil',
        ];
    }
}
