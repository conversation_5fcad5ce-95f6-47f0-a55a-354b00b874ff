<?php

namespace App\Http\Controllers\Admin;

use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use App\Exceptions\BusinessLogicException;
use App\Exceptions\DatabaseException;
use App\Exceptions\NotFoundException;
use App\Http\Controllers\Controller;
use App\Http\Requests\UserRequests\UserChangeStatusRequest;
use App\Http\Requests\UserRequests\UserFilterRequest;
use App\Http\Requests\UserRequests\UserStoreRequest;
use App\Http\Requests\UserRequests\UserUpdateRequest;
use App\Models\User;
use App\Services\UserService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class UserController extends Controller
{
    /**
     * UserController constructor
     */
    public function __construct(protected UserService $userService)
    {
    }

    /**
     * Display a listing of users
     */
    public function index(UserFilterRequest $request): View|JsonResponse
    {
        $users = $this->userService->getAllUsers($request->validated());

        if ($request->ajax()) {
            return $this->datatableResponse($users);
        }

        return view('admin.pages.user.index', [
            'users' => $users,
            'roles' => RoleEnum::dropdown(),
            'statuses' => UserStatus::dropdown(),
        ]);
    }

    /**
     * Format response for DataTables
     */
    private function datatableResponse($data): JsonResponse
    {
        return datatables()
            ->of($data)
            ->addIndexColumn()
            ->editColumn('name', fn($row) => $row->name ?? '-')
            ->editColumn('email', fn($row) => $row->email ?? '-')
            ->editColumn('role', fn($row) => $row->roleLabel() ?? '-')
            ->editColumn('status', function ($row) {
                return '<span class="badge bg-' . $row->status->color() . ' text-uppercase">' . $row->status->label() . '</span>';
            })
            ->addColumn(
                'action',
                fn($row) => view('admin.components.button-actions-v2', [
                    'id' => $row->id,
                    'edit' => route('admin.users.edit', $row->id),
                    'destroy' => route('admin.users.destroy', $row->id),
                ])
            )
            ->rawColumns(['status', 'action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new user
     */
    public function create(): View
    {
        return view('admin.pages.user.create', [
            'roles' => RoleEnum::dropdown(),
            'statuses' => UserStatus::dropdown(),
        ]);
    }

    /**
     * Store a newly created user
     */
    public function store(UserStoreRequest $request): JsonResponse
    {
        try {
            $user = $this->userService->createUser($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Pengguna berhasil dibuat',
                'data' => $user,
            ]);
        } catch (BusinessLogicException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_BAD_REQUEST);
        } catch (DatabaseException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            return $this->errorResponse('Terjadi kesalahan saat membuat pengguna', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Show the form for editing the specified user
     */
    public function edit(User $user): View
    {
        return view('admin.pages.user.edit', [
            'user' => $user,
            'roles' => RoleEnum::dropdown(),
            'statuses' => UserStatus::dropdown(),
            'currentRole' => $user->getRoleNames()->first(),
        ]);
    }

    /**
     * Update the specified user
     */
    public function update(UserUpdateRequest $request, User $user): JsonResponse
    {
        try {
            $this->userService->updateUser($user->id, $request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Pengguna berhasil diperbarui',
            ]);
        } catch (BusinessLogicException $e) {
            return response()->json([
                'success' => true,
                'message' => $e->getMessage(),
            ]);
        } catch (NotFoundException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (DatabaseException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            return $this->errorResponse('Terjadi kesalahan saat memperbarui pengguna', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified user
     */
    public function destroy(User $user): JsonResponse
    {
        try {
            $this->userService->deleteUser($user->id);

            return response()->json([
                'success' => true,
                'message' => 'Pengguna berhasil dihapus',
            ]);
        } catch (BusinessLogicException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_BAD_REQUEST);
        } catch (NotFoundException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (DatabaseException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            return $this->errorResponse('Terjadi kesalahan saat menghapus pengguna', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Change user status
     */
    public function changeStatus(UserChangeStatusRequest $request): JsonResponse
    {
        try {
            $validated = $request->validated();
            $this->userService->changeStatus($validated['id'], $validated['status']);

            $status = $validated['status'] ? 'aktif' : 'nonaktif';

            return response()->json([
                'success' => true,
                'message' => "Status pengguna berhasil diubah menjadi {$status}",
            ]);
        } catch (BusinessLogicException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_BAD_REQUEST);
        } catch (NotFoundException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_NOT_FOUND);
        } catch (DatabaseException $e) {
            return $this->errorResponse($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        } catch (\Exception $e) {
            return $this->errorResponse('Terjadi kesalahan saat mengubah status pengguna', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Generate standardized error response
     */
    protected function errorResponse(string $message, int $statusCode = Response::HTTP_BAD_REQUEST, ?array $errors = null, array $headers = []): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'errors' => $errors,
        ], $statusCode, $headers);
    }
}
