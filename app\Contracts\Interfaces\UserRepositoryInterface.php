<?php

namespace App\Contracts\Interfaces;

use App\Models\User;
use Illuminate\Database\Eloquent\Collection;

interface UserRepositoryInterface
{
    public function getUsersByRole(string $roleName);
    public function getUserById(int $userId);
    public function deleteUser(int $userId);
    public function createUser(array $userDetails);
    public function updateUser(int $userId, array $newDetails);
}
