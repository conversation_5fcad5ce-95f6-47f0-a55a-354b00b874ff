<?php

namespace App\Contracts\Interfaces;

use App\Models\User;
use Illuminate\Database\Eloquent\Collection;

interface UserRepositoryInterface
{
    /**
     * Get all users with optional filtering
     */
    public function getAll(array $filters = []): Collection;

    /**
     * Find a user by ID
     *
     * @throws \App\Exceptions\NotFoundException
     */
    public function findById(int $id): User;

    /**
     * Get users by role
     */
    public function getUsersByRole(string $roleName): Collection;

    /**
     * Get user by ID (legacy method - use findById instead)
     *
     * @deprecated Use findById instead
     */
    public function getUserById(int $userId): User;

    /**
     * Create a new user
     */
    public function create(array $data): User;

    /**
     * Create user (legacy method - use create instead)
     *
     * @deprecated Use create instead
     */
    public function createUser(array $userDetails): User;

    /**
     * Update an existing user
     */
    public function update(int $id, array $data): bool;

    /**
     * Update user (legacy method - use update instead)
     *
     * @deprecated Use update instead
     */
    public function updateUser(int $userId, array $newDetails): bool;

    /**
     * Delete a user
     */
    public function delete(int $id): bool;

    /**
     * Delete user (legacy method - use delete instead)
     *
     * @deprecated Use delete instead
     */
    public function deleteUser(int $userId): bool;

    /**
     * Get all users (legacy method - use getAll instead)
     *
     * @deprecated Use getAll instead
     */
    public function getAllUsers(array $filters = []): Collection;

    /**
     * Count users by role
     */
    public function countUsersByRole(string $role): int;

    /**
     * Count active users by role
     */
    public function countActiveUsersByRole(string $role): int;

    /**
     * Change user status
     */
    public function changeStatus(int $id, bool $status): bool;

    /**
     * Verify user credentials
     */
    public function verifyCredentials(string $username, string $password): ?User;

    /**
     * Find user by email
     */
    public function findByEmail(string $email): ?User;

    /**
     * Find user by username
     */
    public function findByUsername(string $username): ?User;
}
