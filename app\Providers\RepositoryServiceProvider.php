<?php

namespace App\Providers;

use App\Contracts\Interfaces\ClassroomRepositoryInterface;
use App\Contracts\Interfaces\ShiftRepositoryInterface;
use App\Repositories\ClassroomRepository;
use App\Repositories\ShiftRepository;
use Illuminate\Support\ServiceProvider;

final class RepositoryServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(ClassroomRepositoryInterface::class, ClassroomRepository::class);
        $this->app->bind(ShiftRepositoryInterface::class, ShiftRepository::class);
    }
}
