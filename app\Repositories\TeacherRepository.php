<?php

namespace App\Repositories;

use App\Contracts\Interfaces\TeacherRepositoryInterface;
use App\Contracts\Interfaces\UserRepositoryInterface;
use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use App\Exceptions\DatabaseException;
use App\Exceptions\NotFoundException;
use App\Models\Teacher;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;

class TeacherRepository implements TeacherRepositoryInterface
{
    public function __construct(
        private Teacher $teacherModel,
        private UserRepositoryInterface $userRepository
    ) {
    }

    public function getAll(array $filters = []): Collection
    {
        return $this->teacherModel->with('user')
            ->when(isset($filters['status']), fn($q) => $q->whereHas('user', fn($q) => $q->where('status', $filters['status'])))
            ->when(isset($filters['role']), fn($q) => $q->whereHas('user', fn($q) => $q->whereHas('roles', fn($q) => $q->where('name', $filters['role']))))
            ->when(isset($filters['search']), fn($q) => $q->whereHas('user', $this->getSearchFilter($filters['search'])))
            ->when(isset($filters['gender']), fn($q) => $q->where('gender', $filters['gender']))
            ->orderByDesc('created_at')
            ->get();
    }

    public function getAllActive(): Collection
    {
        return $this->teacherModel->with('user')
            ->whereHas('user', fn($q) => $q->where('status', UserStatus::Active))
            ->orderByDesc('created_at')
            ->get();
    }

    public function findById(int $id): Teacher
    {
        $teacher = $this->teacherModel->with('user')->find($id);

        if (!$teacher) {
            throw new NotFoundException("Guru dengan ID {$id} tidak ditemukan");
        }

        return $teacher;
    }

    public function create(array $data): Teacher
    {
        try {
            $teacher = $this->teacherModel->create([
                'birth_place' => $data['birth_place'],
                'birth_date' => $data['birth_date'],
                'gender' => $data['gender'],
                'phone_number' => $data['phone_number'] ?? null,
                'full_address' => $data['full_address'] ?? null,
                'user_id' => $data['user_id'],
            ]);

            return $teacher;
        } catch (Throwable $e) {
            throw new DatabaseException("Gagal membuat data guru: {$e->getMessage()}", 0, [], [], 0, $e);
        }
    }

    public function update(int $id, array $data): bool
    {
        try {
            $teacher = $this->findById($id);
            return $teacher->update([
                'birth_place' => $data['birth_place'] ?? $teacher->birth_place,
                'birth_date' => $data['birth_date'] ?? $teacher->birth_date,
                'gender' => $data['gender'] ?? $teacher->gender,
                'phone_number' => $data['phone_number'] ?? $teacher->phone_number,
                'full_address' => $data['full_address'] ?? $teacher->full_address,
            ]);
        } catch (NotFoundException $e) {
            throw $e;
        } catch (Throwable $e) {
            throw new DatabaseException("Gagal memperbarui data guru: {$e->getMessage()}", 0, [], [], 0, $e);
        }
    }

    public function getAvailableTeachers(array $filters): Collection
    {
        return $this->teacherModel->with('user')
            ->when(isset($filters['gender']), fn($q) => $q->where('gender', $filters['gender']))
            ->get();
    }

    public function getTeacherAssignments(int $teacherId): Collection
    {
        return $this->findById($teacherId)
            ->teacherAssignments()
            ->with('subject', 'classroom')
            ->get();
    }

    public function getTeacherSchedule(int $teacherId, string $date): Collection
    {
        return $this->findById($teacherId)
            ->teacherAssignments()
            ->with([
                'classSchedules' => fn($q) => $q->whereDate('date', $date),
                'subject',
                'classroom',
            ])
            ->get();
    }

    public function count(): int
    {
        return $this->teacherModel->count();
    }

    public function delete(int $id): bool
    {
        try {
            $teacher = $this->findById($id);
            return $teacher->delete();
        } catch (NotFoundException $e) {
            throw $e;
        } catch (Throwable $e) {
            throw new DatabaseException("Gagal menghapus data guru: {$e->getMessage()}", 0, [], [], 0, $e);
        }
    }

    public function createTeacherWithUser(array $data): Teacher
    {
        try {
            $user = $this->userRepository->createUser([
                'name' => $data['name'],
                'username' => $data['username'],
                'email' => $data['email'],
                'password' => $data['password'],
                'phone_number' => $data['phone_number'] ?? null,
                'role' => $data['role'] ?? RoleEnum::SUBJECT_TEACHER->value,
                'status' => $data['status'] ?? UserStatus::Active,
            ]);

            $teacherData = [
                'birth_place' => $data['birth_place'],
                'birth_date' => $data['birth_date'],
                'gender' => $data['gender'],
                'full_address' => $data['full_address'] ?? null,
                'phone_number' => $data['phone_number'] ?? null,
                'user_id' => $user->id,
            ];

            return $this->create($teacherData)->load('user');
        } catch (Throwable $e) {
            throw new DatabaseException("Gagal membuat data guru: {$e->getMessage()}", 0, [], [], 0, $e);
        }
    }

    public function updateTeacherWithUser(int $id, array $data): bool
    {
        try {
            $teacher = $this->findById($id);
            $this->updateUserData($teacher->user_id, $data);
            return $this->update($id, $data);
        } catch (NotFoundException $e) {
            throw $e;
        } catch (Throwable $e) {
            throw new DatabaseException("Gagal memperbarui data guru: {$e->getMessage()}", 0, [], [], 0, $e);
        }
    }

    private function getSearchFilter(string $searchTerm): callable
    {
        $term = '%' . $searchTerm . '%';

        return fn($q) => $q->where('name', 'like', $term)
            ->orWhere('email', 'like', $term)
            ->orWhere('username', 'like', $term)
            ->orWhere('phone_number', 'like', $term);
    }

    private function updateUserData(int $userId, array $data): bool
    {
        $userData = array_filter([
            'name' => $data['name'] ?? null,
            'email' => $data['email'] ?? null,
            'username' => $data['username'] ?? null,
            'phone_number' => $data['phone_number'] ?? null,
            'password' => $data['password'] ?? null,
            'role' => $data['role'] ?? null,
        ]);

        return !empty($userData)
            ? $this->userRepository->updateUser($userId, $userData)
            : false;
    }
}
