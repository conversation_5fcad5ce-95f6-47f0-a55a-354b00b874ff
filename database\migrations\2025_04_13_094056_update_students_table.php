<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('students', function (Blueprint $table) {
            // Make phone_number nullable and rename it
            $table->string('phone_number', 20)->nullable()->change();
            $table->renameColumn('phone_number', 'phone');

            // Add new columns
            $table->string('nis', 20)->unique()->after('user_id');
            $table->string('nisn', 20)->unique()->after('nis');
            $table->string('religion', 20)->after('gender');
            $table->text('address')->after('religion');
            $table->string('parent_name')->after('address');
            $table->string('parent_phone', 15)->after('parent_name');
            $table->string('parent_occupation', 100)->nullable()->after('parent_phone');
            $table->text('parent_address')->nullable()->after('parent_occupation');
            $table->year('entry_year')->after('parent_address');
            $table->string('profile_picture')->nullable()->after('entry_year');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('students', function (Blueprint $table) {
            // Rename back phone to phone_number
            $table->renameColumn('phone', 'phone_number');

            // Revert phone_number to required
            $table->string('phone_number', 20)->nullable(false)->change();

            // Drop added columns
            $table->dropColumn([
                'nis',
                'nisn',
                'religion',
                'address',
                'parent_name',
                'parent_phone',
                'parent_occupation',
                'parent_address',
                'entry_year',
                'profile_picture',
            ]);
        });
    }
};
