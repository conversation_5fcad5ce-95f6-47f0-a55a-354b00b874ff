<?php

namespace App\Contracts\Interfaces;

interface ReportRepositoryInterface
{
    /**
     * Generate attendance report for a classroom
     *
     * @param  int  $classroomId  The ID of the classroom
     * @param  array  $params  Report parameters
     * @return mixed The generated attendance report
     */
    public function generateAttendanceReport(int $classroomId, array $params);

    /**
     * Generate attendance report for a teacher
     *
     * @param  int  $teacherId  The ID of the teacher
     * @param  array  $params  Report parameters
     * @return mixed The generated teacher attendance report
     */
    public function generateTeacherAttendanceReport(int $teacherId, array $params);

    /**
     * Generate academic report for a student
     *
     * @param  int  $studentId  The ID of the student
     * @param  int  $academicYearId  The ID of the academic year
     * @return mixed The generated academic report
     */
    public function generateAcademicReport(int $studentId, int $academicYearId);

    /**
     * Generate summary report for a classroom
     *
     * @param  int  $classroomId  The ID of the classroom
     * @return mixed The generated classroom summary
     */
    public function generateClassroomSummary(int $classroomId);

    /**
     * Export report data
     *
     * @param  array  $params  Export parameters
     * @return mixed The exported report data
     */
    public function exportReportData(array $params);
}
