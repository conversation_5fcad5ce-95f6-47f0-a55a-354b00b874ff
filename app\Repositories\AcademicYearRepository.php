<?php

namespace App\Repositories;

use App\Contracts\Interfaces\AcademicYearRepositoryInterface;
use App\Models\AcademicYear;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

final class AcademicYearRepository implements AcademicYearRepositoryInterface
{
    public function getAllWithRelations(): Collection
    {
        return AcademicYear::with(['classrooms', 'teacherAssignments'])
            ->orderBy('start_date', 'desc')
            ->get();
    }

    /**
     * Get all academic years with optional filtering
     *
     * @param  array  $filters  Optional filters
     */
    public function getAll(array $filters = []): Collection
    {
        $query = AcademicYear::query();

        // Apply search filter if provided
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }

        // Apply status filter if provided
        if (!empty($filters['status'])) {
            if ($filters['status'] === 'Aktif') {
                $query->where('status', 'active');
            } elseif ($filters['status'] === 'Tidak Aktif') {
                $query->where('status', 'inactive');
            }
        }

        // Apply semester filter if provided
        if (!empty($filters['semester'])) {
            if ($filters['semester'] === 'Ganjil') {
                $query->where('semester', 'ganjil');
            } elseif ($filters['semester'] === 'Genap') {
                $query->where('semester', 'genap');
            }
        }

        return $query->orderBy('start_date', 'desc')->get();
    }

    public function getAllPaginated(int $perPage = 10): LengthAwarePaginator
    {
        return AcademicYear::query()
            ->orderBy('start_date', 'desc')
            ->paginate($perPage);
    }

    /**
     * Find academic year by ID
     */
    public function findById(int $id): AcademicYear
    {
        return AcademicYear::findOrFail($id);
    }

    public function create(array $data): AcademicYear
    {
        return AcademicYear::create($data);
    }

    /**
     * Update academic year
     *
     * @throws \Exception
     */
    public function update(int $id, array $data): AcademicYear
    {
        DB::beginTransaction();
        try {
            $academicYear = $this->findById($id);
            $academicYear->update($data);
            DB::commit();

            return $academicYear;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Update academic year failed: ' . $e->getMessage());
            throw new \Exception('Gagal memperbarui tahun akademik: ' . $e->getMessage());
        }
    }

    public function delete(AcademicYear $academicYear): bool
    {
        return $academicYear->delete();
    }

    public function getActive(): ?AcademicYear
    {
        return AcademicYear::where('status', 'active')->first();
    }

    /**
     * Get current academic year
     */
    public function getCurrentAcademicYear(): ?AcademicYear
    {
        return AcademicYear::where('status', 'active')
            ->whereDate('start_date', '<=', now())
            ->whereDate('end_date', '>=', now())
            ->first();
    }

    /**
     * Change academic year status
     */
    public function changeStatus(int $id, string $status): AcademicYear
    {
        $academicYear = $this->findById($id);
        $academicYear->update(['status' => $status]);

        return $academicYear;
    }

    /**
     * Get all active academic years
     */
    public function getActiveYears(): Collection
    {
        return AcademicYear::where('status', 'active')->get();
    }

    /**
     * Check if academic year has related data
     */
    public function hasRelatedData(int $id): bool
    {
        $academicYear = $this->findById($id);

        return $academicYear->classrooms()->count() > 0 || $academicYear->teacherAssignments()->count() > 0;
    }

    /**
     * Get a list of all academic years for API
     */
    public function getAcademicYearsList(): Collection
    {
        return AcademicYear::orderBy('status', 'desc')
            ->orderBy('name', 'desc')
            ->get(['id', 'name', 'status']);
    }

    /**
     * Get academic year with its relations
     */
    public function getAcademicYearWithRelations(int $id): AcademicYear
    {
        return AcademicYear::with(['classrooms', 'teacherAssignments'])->findOrFail($id);
    }
}
